<script setup lang="ts">
  import {useTypedI18n} from '@/language/index.ts'
  import {useLanguageStore} from '@/stores/language.js'
  import {computed, onMounted, reactive, ref} from 'vue'
  import {useRoute, useRouter, type RouteLocationNormalizedLoaded, type Router} from 'vue-router'
  import {PATH_NAME} from '../../defined/const.js'
  import {useCognitoAuthStore} from '../../stores/cognitoAuth.ts'

  // Router and route
  const route: RouteLocationNormalizedLoaded = useRoute()
  const router: Router = useRouter()
  const cognitoAuth = useCognitoAuthStore()
  const {t: translate} = useTypedI18n()
  const languageStore = useLanguageStore()

  // Form data
  const formData = reactive({
    email: '',
    password: '',
    rememberLogin: false,
    agreeToTerms: false,
  })

  // Form state
  const loading = ref(false)
  const loginErrors = ref<string[]>([])
  const showErrors = ref(false)

  // Form validation
  const formErrors = reactive({
    email: '',
    password: '',
    agreeToTerms: '',
  })

  // Validation functions
  const validateEmail = (email: string): string => {
    if (!email) return translate('LOGIN_VALIDATION_NOT_ENTERED')
    return ''
  }

  const validatePassword = (password: string): string => {
    if (!password) return translate('LOGIN_VALIDATION_NOT_ENTERED')
    if (password.length < 8 || password.length > 14)
      return translate('LOGIN_VALIDATION_PASSWORD_LENGTH')
    return ''
  }

  const validateAgreeToTerms = (agreed: boolean): string => {
    if (!agreed) return translate('LOGIN_VALIDATION_AGREE_TERMS')
    return ''
  }

  // Validate individual field
  const validateField = (fieldName: keyof typeof formData) => {
    switch (fieldName) {
      case 'email':
        formErrors.email = validateEmail(formData.email)
        break
      case 'password':
        formErrors.password = validatePassword(formData.password)
        break
      case 'agreeToTerms':
        formErrors.agreeToTerms = validateAgreeToTerms(formData.agreeToTerms)
        break
    }
  }

  // Check if form is valid
  const isFormValid = computed(() => {
    return (
      formData.email &&
      formData.password &&
      formData.agreeToTerms &&
      !formErrors.email &&
      !formErrors.password &&
      !formErrors.agreeToTerms
    )
  })

  // Handle login
  const handleLogin = async () => {
    // Validate all fields
    validateField('email')
    validateField('password')
    validateField('agreeToTerms')

    // Show errors if validation fails
    if (!isFormValid.value) {
      showErrors.value = true
      return
    }

    loading.value = true
    loginErrors.value = []
    showErrors.value = false

    try {
      const result = await cognitoAuth.login(formData.email, formData.password)

      if (result.type === 'SUCCESS') {
        // Successful login - redirect to intended page or top
        const redirectPath =
          (route.query.redirect as string) || route.redirectedFrom?.path || PATH_NAME.TOP
        router.replace(redirectPath)
      } else if (result.type === 'NEW_PASSWORD_REQUIRED') {
        // Handle new password requirement
        loginErrors.value = [result.message || translate('LOGIN_ERROR_NEW_PASSWORD_REQUIRED')]
        showErrors.value = true
      } else if (result.type === 'NEED_OTP') {
        // Handle OTP requirement - redirect to OTP page
        router.push(PATH_NAME.OTP)
      } else {
        // Handle other error types
        loginErrors.value = [result.message || translate('LOGIN_ERROR_LOGIN_FAILED')]
        showErrors.value = true
      }
    } catch (error: any) {
      console.error('Login error:', error)

      // Handle specific Cognito errors
      if (error.message) {
        if (error.message.includes('User is disabled')) {
          loginErrors.value = [translate('LOGIN_ERROR_INVALID_CREDENTIALS')]
        } else if (error.message.includes('NotAuthorizedException')) {
          loginErrors.value = [translate('LOGIN_ERROR_INVALID_CREDENTIALS')]
        } else {
          loginErrors.value = [error.message]
        }
      } else {
        loginErrors.value = [translate('LOGIN_ERROR_LOGIN_FAILED')]
      }
      showErrors.value = true
    } finally {
      loading.value = false
    }
  }

  const navigateToReminder = () => {
    router.push(PATH_NAME.REMINDER)
  }

  // Hide errors on component mount for clean initial state
  onMounted(() => {
    showErrors.value = false
    loginErrors.value = []
  })
</script>

<template>
  <h2 class="page-ttl">
    <p class="ttl">{{ translate('LOGIN_TITLE') }}</p>
    <p class="sub">{{ translate('LOGIN_SUBTITLE') }}</p>
  </h2>
  <div class="container">
    <section id="login-form">
      <form @submit.prevent="handleLogin">
        <!-- Global Error Messages -->
        <div v-if="showErrors && loginErrors.length > 0" class="id-pass-err">
          <span v-for="(error, index) in loginErrors" :key="index" class="err-txt">
            {{ error }}
          </span>
        </div>

        <table class="tbl-login">
          <tbody>
            <tr>
              <th>
                {{ translate('LOGIN_ID') }}<em class="req">{{ translate('LOGIN_REQUIRED') }}</em>
              </th>
              <td>
                <input
                  v-model="formData.email"
                  type="text"
                  :class="[
                    'ime-dis',
                    {
                      err: showErrors && formErrors.email,
                    },
                  ]"
                  :placeholder="translate('LOGIN_PLACEHOLDER_ID_PASSWORD')"
                  autocomplete="username"
                  @blur="validateField('email')"
                  @input="formErrors.email = ''"
                  required
                />
                <p v-if="showErrors && formErrors.email" class="err-txt">
                  {{ formErrors.email }}
                </p>
              </td>
            </tr>
            <tr>
              <th>
                {{ translate('LOGIN_PASSWORD')
                }}<em class="req">{{ translate('LOGIN_REQUIRED') }}</em>
              </th>
              <td>
                <input
                  v-model="formData.password"
                  type="password"
                  :class="[
                    'ime-dis',
                    {
                      err: showErrors && formErrors.password,
                    },
                  ]"
                  :placeholder="translate('LOGIN_PLACEHOLDER_ID_PASSWORD')"
                  autocomplete="current-password"
                  @blur="validateField('password')"
                  @input="formErrors.password = ''"
                  required
                />
                <p v-if="showErrors && formErrors.password" class="err-txt">
                  {{ formErrors.password }}
                </p>
              </td>
            </tr>
          </tbody>
        </table>
        <div class="check-idpass">
          <label>
            <input v-model="formData.rememberLogin" type="checkbox" class="checkbox-input" />
            <span class="checkbox-parts">{{ translate('LOGIN_SAVE_ID_PASSWORD') }}</span>
          </label>
        </div>
        <div class="forget-pass">
          <a @click="navigateToReminder">{{ translate('LOGIN_FORGET_PASSWORD') }}</a>
        </div>

        <div class="rule">
          <p class="tit-rule">{{ translate('LOGIN_RULE_TITLE') }}</p>
          <embed
            :src="languageStore.currentLanguage === 'ja' ? '/pdf/sample.pdf' : '/pdf/sample-en.pdf'"
            type="application/pdf"
            width="100%"
            height="150"
          />
          <div class="rule-check">
            <label for="rule-chk">
              <input
                v-model="formData.agreeToTerms"
                type="checkbox"
                id="rule-chk"
                :class="[
                  'checkbox-input',
                  {
                    err: showErrors && formErrors.agreeToTerms,
                  },
                ]"
                @change="validateField('agreeToTerms')"
                required
              />
              <span class="checkbox-parts">{{ translate('LOGIN_AGREE_RULE') }}</span>
            </label>
            <p v-if="showErrors && formErrors.agreeToTerms" class="err-txt">
              {{ formErrors.agreeToTerms }}
            </p>
          </div>
        </div>
        <div class="btn-form">
          <input
            type="submit"
            id="sbm-login"
            :disabled="loading || !isFormValid"
            :value="loading ? translate('LOGIN_PROCESSING') : translate('LOGIN_CONFIRM_BUTTON')"
            :class="{loading: loading}"
          />
        </div>
      </form>
      <div class="request">
        <a class="register-btt" @click="() => router.push(PATH_NAME.ENTRY_INFO_REGIST)">
          {{ translate('LOGIN_NEW_MEMBER_REGISTER') }}
        </a>
        <span class="mx-2">|</span>
        <p>{{ translate('LOGIN_PRICE_VIEW_REQUIRES_REGISTRATION') }}</p>
      </div>
    </section>
  </div>
</template>

<style lang="css">
  .register-btt {
    cursor: pointer;
  }

  #sbm-login {
    border-radius: 40px !important;
  }

  /* Loading state for submit button */
  #sbm-login.loading {
    opacity: 0.7;
    cursor: not-allowed;
    background: #ccc;
  }

  #sbm-login:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    background: #ccc;
  }

  /* Form validation feedback */
  .ime-dis:focus {
    outline: none;
    border-color: #427fae;
    box-shadow: 0 0 0 2px rgba(66, 127, 174, 0.2);
  }

  .ime-dis.err:focus {
    border-color: #dc3545;
    box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.2);
  }
</style>
