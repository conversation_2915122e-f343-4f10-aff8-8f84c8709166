import { en as enLocale, ja as ja<PERSON><PERSON>ale } from 'vuetify/locale'

// Define interfaces first
export interface TranslationKeys {
  $vuetify: {
    dataIterator: {
      rowsPerPageText: string
      pageText: string
    }
    [key: string]: any
  }

  // COMMON TRANSLATIONS
  COMMON_BACK: string
  COMMON_BACK_LIST: string
  COMMON_MORE: string
  COMMON_JAPAN: string
  COMMON_DUBAI: string
  COMMON_HONGKONG: string
  COMMON_SEND: string
  COMMON_AGREE: string
  COMMON_ERROR: string
  COMMON_CONFIRM: string
  COMMON_INPUT_ERROR: string
  COMMON_DATE_FORMAT: string
  COMMON_REMOVE: string
  COMMON_UPDATE_AUCTION: string
  COMMON_DAY: string
  COMMON_HOUR: string
  COMMON_MINUTE: string
  COMMON_SECOND: string
  COMMON_MITAIOU: string
  COMMON_LOADING: string
  COMMON_OK_BUTTON: string

  ASCENDING_AUCTION: string
  SEALED_AUCTION: string

  // SITE INFORMATION
  SITE_TITLE: string
  COPYRIGHT: string

  // TOP PAGE APP BAR
  TOP_APP_BAR_SELECT_CATEGORY: string
  TOP_APP_BAR_REGISTER: string
  TOP_APP_BAR_LOGIN: string
  TOP_APP_BAR_LOGOUT: string
  TOP_SEARCH_BY_CATEGORY: string
  TOP_SEARCH_ALL_CATEGORIES: string
  TOP_SELECT_CATEGORY: string
  TOP_UPDATE_INFO: string
  TOP_RECOMMEND_PRODUCT: string
  TOP_NEW_PRODUCTS: string
  TOP_ABOUT_TITLE: string
  TOP_ABOUT_SUBTITLE: string
  TOP_ABOUT_DESCRIPTION_1: string
  TOP_ABOUT_DESCRIPTION_2: string
  TOP_ABOUT_DESCRIPTION_3: string
  TOP_ABOUT_DESCRIPTION_4: string
  TOP_SIGNUP_SUBTITLE: string
  TOP_SIGNUP_TITLE: string
  TOP_SIGNUP_FREE_SHIPPING_TITLE: string
  TOP_SIGNUP_FREE_SHIPPING_DESC: string
  TOP_SIGNUP_FREE_SHIPPING_LINK: string
  TOP_SIGNUP_FREE_SHIPPING_SUFFIX: string
  TOP_SIGNUP_COUPON_TITLE: string
  TOP_SIGNUP_COUPON_DESC: string
  TOP_SIGNUP_COUPON_LOGIN_LINK: string
  TOP_SIGNUP_COUPON_SUFFIX: string
  TOP_SIGNUP_MEMBER_MORE_BUTTON: string
  TOP_SIGNUP_REGISTER_BUTTON: string

  // HEADER NAVIGATION
  HEADER_NAV_PRODUCT_CATEGORY: string
  HEADER_NAV_ALL_CATEGORIES: string
  HEADER_NAV_CATEGORY_1: string
  HEADER_NAV_CATEGORY_2: string
  HEADER_NAV_CATEGORY_3: string
  HEADER_NAV_SEARCH_PLACEHOLDER: string
  HEADER_NAV_SITE_ABOUT: string
  HEADER_NAV_FIRST_TIME_VISITORS: string
  HEADER_NAV_SHOPPING_GUIDE: string
  HEADER_NAV_FAQ: string
  HEADER_NAV_CONTACT_US: string
  HEADER_NAV_MEMBER_SERVICES: string
  HEADER_NAV_LOGIN: string
  HEADER_NAV_FAVORITES: string
  HEADER_NAV_BIDDING: string
  HEADER_NAV_SUCCESSFUL_BID_HISTORY: string
  HEADER_NAV_REGISTER: string
  HEADER_NAV_LOGOUT: string
  HEADER_NAV_NEW_MEMBER_REGISTER: string
  HEADER_NAV_MEMBER_MENU: string
  HEADER_NAV_MY_PAGE: string
  HEADER_NAV_GUIDANCE: string
  HEADER_NAV_COMPANY_INFO: string
  HEADER_NAV_SERVICE: string
  HEADER_NAV_MEMBERSHIP: string
  HEADER_NAV_TOKUSHO: string
  HEADER_NAV_TERMS_OF_SERVICE: string
  HEADER_NAV_SPECIFIED_COMMERCIAL_TRANSACTION_LAW: string
  HEADER_NAV_PRIVACY_POLICY: string // FOOTER NAVIGATION
  FOOTER_NAV_PRODUCT_PURCHASE: string
  FOOTER_NAV_BAGS: string
  FOOTER_NAV_OUTERWEAR: string
  FOOTER_NAV_ACCESSORIES: string
  FOOTER_NAV_ABOUT_MEMBERSHIP: string
  FOOTER_NAV_LOGIN: string
  FOOTER_NAV_NEW_MEMBER_REGISTRATION: string
  FOOTER_NAV_MY_PAGE: string
  FOOTER_NAV_FIRST_TIME_VISITORS: string
  FOOTER_NAV_USAGE_GUIDE: string
  FOOTER_NAV_FAQ: string
  FOOTER_NAV_MEMBER_SERVICES: string
  FOOTER_NAV_GUIDANCE: string
  FOOTER_NAV_NEWS: string
  FOOTER_NAV_TERMS_OF_SERVICE: string
  FOOTER_NAV_COMPANY_OVERVIEW: string
  FOOTER_NAV_CAMPAIGN_EARLY_BIRD: string
  FOOTER_NAV_COUPON_PRODUCTS: string
  FOOTER_NAV_OFFICE_SUPPLIES: string
  FOOTER_NAV_COMPUTERS: string
  FOOTER_NAV_AV_ELECTRONICS: string
  FOOTER_NAV_NEW_MEMBER_DISCOUNT: string
  FOOTER_NAV_STORE_FIXTURES: string
  FOOTER_NAV_CONSTRUCTION_MATERIALS: string
  FOOTER_NAV_COMPANY_GUIDE: string
  FOOTER_NAV_PRIVACY_POLICY: string
  FOOTER_NAV_COMMERCIAL_TRANSACTION_LAW: string

  // ROUTE/PAGE TITLES
  ROUTE_TOP: string
  NYUSATSU_KAKAKU: string

  // PRODUCT DETAIL PAGE
  DETAIL_TITLE: string
  DETAIL_DESCRIPTION: string
  DETAIL_CURRENCY: string
  DETAIL_QUANTITY: string
  DETAIL_LOWEST_BID_QUANTITY: string
  DETAIL_LOWEST_BID_PRICE: string
  DETAIL_BID_COUNT: string
  DETAIL_BID_QUANTITY: string
  DETAIL_BID_UNIT_PRICE: string
  DETAIL_BID_TOTAL_PRICE: string
  DETAIL_BID_BUTTON: string
  DETAIL_CONTACT_BUTTON: string
  DETAIL_ABOUT_RANK: string
  DETAIL_CHAT: string
  DETAIL_VIEW_COMMENTS: string
  DETAIL_CANCEL_BID: string
  DETAIL_BID_HISTORY: string
  DETAIL_LAST_BID_TIME: string
  DETAIL_LAST_BID_AMOUNT: string
  DETAIL_HIGHEST_BID_AMOUNT: string
  DETAIL_BACK_TO_LIST: string
  UNIT_BID_AVAILABLE: string
  SCHEDULED_END: string
  SHARE_THIS_PRODUCT: string

  // PRODUCT DETAIL INFO SECTION
  DETAIL_INFO_MAKER: string
  DETAIL_INFO_PRODUCT_NAME: string
  DETAIL_INFO_SIM: string
  DETAIL_INFO_CAPACITY: string
  DETAIL_INFO_COLOR: string
  DETAIL_INFO_RANK: string
  DETAIL_INFO_QUANTITY: string
  DETAIL_INFO_NOTE1: string
  DETAIL_INFO_NOTE2: string
  DETAIL_INFO_LOWEST_BID_PRICE: string
  DETAIL_INFO_LOWEST_BID_QUANTITY: string
  DETAIL_INFO_FAVORITE: string
  DETAIL_INFO_START_PRICE: string
  DETAIL_INFO_CURRENT_PRICE: string
  DETAIL_INFO_TAX_INCLUDED_PRICE: string
  DETAIL_DEAL_BID_PRICE: string
  DETAIL_BID_PERIOD: string

  // PRODUCT DETAIL BID MODAL
  ERROR_BID_MODAL_LOGIN_REQUIRED_MESSAGE: string
  BID_MODAL_LOGIN_REQUIRED: string
  BID_MODAL_BID_CANCELLED_SUCCESS: string
  BID_MODAL_CONFIRM_MESSAGE: string
  BID_MODAL_CANCEL_CONFIRMATION: string
  BID_MODAL_CONFIRM_BID: string
  BID_VALIDATION_ERROR: string
  BID_ERROR_OCCURRED: string
  BID_SUCCESS_MESSAGE: string

  // PRODUCT DETAIL ERROR MESSAGES
  ERROR_PRICE_EMPTY: string
  ERROR_PRICE_INVALID_FORMAT: string
  ERROR_PRICE_MAX_LENGTH: string
  ERROR_LOWEST_UNIT_PRICE_INVALID: string
  ERROR_NEW_PRICE_LOWER_THAN_CURRENT_PRICE_ERR: string
  ERROR_QUANTITY_EMPTY: string
  ERROR_QUANTITY_INVALID_FORMAT: string
  ERROR_QUANTITY_MAX_LENGTH: string
  ERROR_LOWEST_BID_QUANTITY_INVALID: string
  ERROR_BID_QUANTITY_EXCEEDS_MAX: string

  // Filter box
  FILTER_BOX_TITLE: string
  FILTER_BOX_SEARCH_PLACEHOLDER: string
  FILTER_BOX_KEYWORD_FIELD: string
  FILTER_BOX_SEARCH_ACTION: string
  FILTER_BOX_CATEGORY: string
  FILTER_BOX_CLEAR_CONDITIONS_ACTION: string
  FILTER_BOX_PRODUCT_SEARCH: string
  FILTER_BOX_COUNT_UNIT: string
  SEARCH_RESULT: string
  SEARCH_RESULTS_SORT_RECOMMENDED: string
  SEARCH_RESULTS_SORT_NEWEST: string
  SEARCH_RESULTS_SORT_TIME_REMAINING_ASC: string
  SEARCH_RESULTS_SORT_TIME_REMAINING_DESC: string
  SEARCH_RESULTS_SORT_BID_COUNT_ASC: string
  SEARCH_RESULTS_SORT_BID_COUNT_DESC: string
  SEARCH_RESULTS_SORT_PRICE_ASC: string
  SEARCH_RESULTS_SORT_PRICE_DESC: string
  SEARCH_RESULTS_ON_SALE_ONLY: string
  SEARCH_RESULTS_DISPLAY_COUNT: string
  SEARCH_RESULTS_20_ITEMS: string
  SEARCH_RESULTS_50_ITEMS: string
  SEARCH_RESULTS_100_ITEMS: string
  SEARCH_RESULTS_PAGINATION_OF: string
  SEARCH_RESULTS_PAGINATION_DISPLAYING: string
  SEARCH_RESULTS_DOWNLOAD_CSV_PRODUCT_INFO: string
  SEARCH_RESULTS_DOWNLOAD_CSV_PRODUCT_HANDOVER: string
  PRODUCT_LIST: string

  // List-related translations
  LIST_CURRENT: string
  LIST_LOWEST_BID_PRICE: string
  LIST_END_DATE_PLAN: string

  MYPAGE_AUCTION_COUNT: string

  // Favorite
  FAVORITE_TITLE: string
  FAVORITE_EMPTY: string
  FAVORITE_CLEAR_PRICE_INPUT: string
  FAVORITE_SUB_TOTAL_BID_PRICE_HEADER: string
  FAVORITE_SUB_TOTAL_BID_PRICE: string
  FAVORITE_LOGIN_REQUIRED_FAVORITE: string
  FAVORITE_BID_BUTTON: string
  FAVORITE_RE_BID_BUTTON: string
  FAVORITE_BID_QUANTITY: string
  FAVORITE_DELETE_FAVORITE1: string
  FAVORITE_DELETE_FAVORITE2: string

  // Bid history
  BID_HISTORY_END_DATE: string
  BID_HISTORY_BID_SUCCESS_UNIT_PRICE: string
  BID_HISTORY_BID_SUCCESS_PRICE: string
  BID_HISTORY_BID_SUCCESS_QUANTITY: string
  BID_HISTORY_BID_TOTAL_PRICE: string
  BID_HISTORY_INVOICE_DOWNLOAD: string
  BID_HISTORY_PAYMENT_STATUS_PAID: string
  BID_HISTORY_PAYMENT_STATUS_PENDING: string
  BID_HISTORY_PAYMENT_STATUS_UNPAID: string
  BID_HISTORY_CURRENT_PRICE: string
  BID_HISTORY_ENDED: string

  // Chat
  INQUIRY_CHAT: string
  CHAT_BACK_ITEM: string
  CHAT_CHAT_ROOM: string
  CHAT_SHOP: string
  CHAT_HIDDEN_DESCRIPTION: string
  CHAT_INPUT_PLACEHOLDER: string
  CHAT_SEND_BUTTON: string
  CHAT_CLEAR_BUTTON: string
  CHAT_ATTENTION_1: string
  CHAT_ATTENTION_2: string
  CHAT_ATTENTION_3: string
  ERROR_CHAT_LOGIN_REQUIRED_MESSAGE: string

  // MY Page Member Edit
  MYPAGE_MEMBER_EDIT_LICENSE_COPY: string
  MYPAGE_MEMBER_EDIT_LICENSE_NOTES: string
  MYPAGE_MEMBER_EDIT_LICENSE_NOTES1: string
  MYPAGE_MEMBER_EDIT_BUSINESS_CARD_COPY: string
  MYPAGE_MEMBER_EDIT_DOWNLOAD: string
  MYPAGE_MEMBER_EDIT_CONFIRM_BUTTON: string
  MYPAGE_MEMBER_EDIT_WITHDRAW_BUTTON: string
  MYPAGE_MEMBER_EDIT_REQUIRED_LABEL: string
  MYPAGE_MEMBER_EDIT_UPLOAD_LABEL: string
  MYPAGE_MEMBER_EDIT_COMPLETE_MESSAGE: string
  MYPAGE_MEMBER_EDIT_CONFIRM: string

  // Auth
  AUTH_LOGOUT_MESSAGE: string
  AUTH_LOGOUT: string
  AUTH_CLOSE: string
  AUTH_CANCEL: string

  // Cognito standardized error messages
  AUTH_COGNITO_USERNAME_EXISTS_EXCEPTION: string
  AUTH_COGNITO_INVALID_PASSWORD_EXCEPTION: string
  AUTH_COGNITO_INVALID_PARAMETER_EXCEPTION: string
  AUTH_COGNITO_CODE_MISMATCH_EXCEPTION: string
  AUTH_COGNITO_LIMIT_EXCEEDED_EXCEPTION: string
  AUTH_COGNITO_EXPIRED_CODE_EXCEPTION: string
  AUTH_COGNITO_NOT_AUTHORIZED_EXCEPTION: string
  AUTH_COGNITO_CODE_DELIVERY_FAILURE_EXCEPTION: string
  AUTH_COGNITO_USER_NOT_CONFIRMED_EXCEPTION: string
  AUTH_COGNITO_PASSWORD_RESET_REQUIRED_EXCEPTION: string
  AUTH_COGNITO_USER_NOT_FOUND_EXCEPTION: string
  AUTH_COGNITO_COGNITO_UNKNOWN_ERROR: string

  // Login
  LOGIN_TITLE: string
  LOGIN_SUBTITLE: string
  LOGIN_ID: string
  LOGIN_EMAIL: string
  LOGIN_EMAIL_CONFIRM: string
  LOGIN_PASSWORD: string
  LOGIN_REQUIRED: string
  LOGIN_SAVE_LOGIN_INFO: string
  LOGIN_FORGET_PASSWORD: string
  LOGIN_RULE: string
  LOGIN_AGREE_RULE: string
  LOGIN_AGREE: string
  LOGIN_ENTRY_INFO1: string
  LOGIN_ENTRY_INFO2: string
  LOGIN_CONFIRM_BUTTON: string
  LOGIN_PASSWORD_HINT: string
  LOGIN_PLACEHOLDER_ID_PASSWORD: string
  LOGIN_PROCESSING: string
  LOGIN_PRICE_VIEW_REQUIRES_REGISTRATION: string
  LOGIN_NEW_MEMBER_REGISTER: string
  LOGIN_FORGET_PASSWORD_LINK: string
  LOGIN_SAVE_ID_PASSWORD: string
  LOGIN_RULE_TITLE: string
  LOGIN_VALIDATION_AGREE_TERMS: string
  LOGIN_VALIDATION_NOT_ENTERED: string
  LOGIN_VALIDATION_PASSWORD_LENGTH: string

  // Login Error Messages
  LOGIN_ERROR_EMAIL_CODE_REQUIRED: string
  LOGIN_ERROR_EMAIL_VERIFICATION_REQUIRED: string
  LOGIN_ERROR_INVALID_CREDENTIALS: string
  LOGIN_ERROR_INVALID_PASSWORD: string
  LOGIN_ERROR_LIMIT_EXCEEDED: string
  LOGIN_ERROR_LOGIN_FAILED: string
  LOGIN_ERROR_LOGIN_HISTORY_FAILED: string
  LOGIN_ERROR_NEW_PASSWORD_REQUIRED: string
  LOGIN_ERROR_TOO_MANY_REQUESTS: string
  LOGIN_ERROR_UNKNOWN: string
  LOGIN_ERROR_UNSUPPORTED_STEP: string
  LOGIN_ERROR_USER_ALREADY_AUTHENTICATED: string
  LOGIN_ERROR_USER_NOT_CONFIRMED: string
  LOGIN_ERROR_INVALID_OTP_CODE: string
  LOGIN_ERROR_OTP_SESSION_EXPIRED: string
  LOGIN_ERROR_MEMBER_STATUS_FAILED: string
  LOGIN_ERROR_LOGIN_FAILED_BY_STATUS: string

  // OTP (One-Time Password) Page
  OTP_TITLE: string
  OTP_SUBTITLE: string
  OTP_EMAIL_SENT_MESSAGE: string
  OTP_ENTER_CODE_INSTRUCTION: string
  OTP_CODE_GROUP_LABEL: string
  OTP_DIGIT_SUFFIX: string
  OTP_AUTHENTICATING: string
  OTP_AUTHENTICATE: string
  OTP_RESEND_CODE: string
  OTP_AUTHENTICATION_FAILED: string
  OTP_INVALID_CODE: string
  OTP_RESEND_IN_DEVELOPMENT: string

  // Password Reminder
  LOGIN_REMINDER_TITLE: string
  LOGIN_REMINDER_SUBTITLE: string
  LOGIN_REMINDER_MESSAGE1: string
  LOGIN_REMINDER_MESSAGE2: string
  LOGIN_REMINDER_USER_ID: string
  LOGIN_REMINDER_USER_ID_PLACEHOLDER: string
  LOGIN_REMINDER_SEND_BUTTON: string
  LOGIN_REMINDER_COMPLETE_MESSAGE: string
  LOGIN_REMINDER_BACK_TO_LOGIN: string
  LOGIN_REMINDER_EMAIL_ERROR: string
  LOGIN_REMINDER_CONFIRM_EMAIL_ERROR: string
  MYPAGE_EDIT_PROFILE: string
  MYPAGE_EDIT_CONFIRM: string
  MYPAGE_CARD: string

  // Payment Form
  PAYMENT_CURRENT_INFO: string
  PAYMENT_COMPLETED: string
  PAYMENT_PENDING: string
  PAYMENT_FAILED: string
  PAYMENT_NOT_PROCESSED: string
  CARD_LIST: string
  CARD_ADD: string
  CARD_SET_DEFAULT: string
  CARD_DELETE: string
  PAYMENT_BUTTON: string
  PAYMENT_COMPLETE: string
  PAYMENT_COMPLETE_MESSAGE: string
  PAYMENT_FAIL: string
  PAYMENT_FAIL_MESSAGE: string
  CARD_DEFAULT_CHANGE_COMPLETE: string
  CARD_DEFAULT_CHANGE_MESSAGE: string
  CARD_DELETE_COMPLETE: string
  CARD_DELETE_MESSAGE: string
  CARD_EDIT: string
  CARD_NO_DATA: string
  CARD_NO: string
  CARD_DEFAULT: string

  CARD_INPUT_REQUIRED: string
  CARD_HOLDER_NAME: string
  CARD_HOLDER_NAME_PLACEHOLDER: string
  CARD_NUMBER: string
  CARD_NUMBER_PLACEHOLDER: string
  CARD_EXPIRATION: string
  CARD_EXPIRATION_MONTH: string
  CARD_EXPIRATION_MONTH_PLACEHOLDER: string
  CARD_EXPIRATION_YEAR: string
  CARD_EXPIRATION_YEAR_PLACEHOLDER: string
  CARD_SECURITY_CODE: string
  CARD_SECURITY_CODE_PLACEHOLDER: string
  CARD_DEFAULT_SETTING: string
  CARD_DEFAULT_SETTING_TEXT: string

  CARD_INPUT_HALF_WIDTH: string
  CARD_COMPLETE_DIALOG_CLOSE: string

  CARD_CONFIRM_DIALOG_DEFAULT_MESSAGE: string
  CARD_CONFIRM_DIALOG_DELETE_MESSAGE: string
  CARD_CONFIRM_DIALOG_CLOSE: string
  CARD_CONFIRM_DIALOG_UPDATE: string
  CARD_CONFIRM_DIALOG_DELETE: string

  CARD_EDIT_DIALOG_TITLE: string
  CARD_EDIT_DIALOG_CARD_NO: string
  CARD_EDIT_DIALOG_EXPIRE: string
  CARD_EDIT_DIALOG_SECURITY_CODE: string
  CARD_EDIT_DIALOG_USE_SETTING: string
  CARD_EDIT_DIALOG_USE_SETTING_TEXT: string
  CARD_EDIT_DIALOG_CONFIRM: string

  PAYMENT_COMPLETE_DIALOG_TITLE: string
  PAYMENT_COMPLETE_DIALOG_MESSAGE: string
  PAYMENT_COMPLETE_DIALOG_NOTE: string
  PAYMENT_COMPLETE_DIALOG_BACK_TO_LIST: string

  PAYMENT_CONFIRM_DIALOG_TITLE: string
  PAYMENT_CONFIRM_DIALOG_MESSAGE: string
  PAYMENT_CONFIRM_DIALOG_PROCESSING: string
  PAYMENT_CONFIRM_DIALOG_CREDIT_CARD: string
  PAYMENT_CONFIRM_DIALOG_CREDIT_CARD_1TIME: string
  PAYMENT_CONFIRM_DIALOG_PRIVACY_POLICY_1: string
  PAYMENT_CONFIRM_DIALOG_PRIVACY_POLICY_2: string
  PAYMENT_CONFIRM_DIALOG_PRIVACY_POLICY_3: string
  PAYMENT_CONFIRM_DIALOG_PRIVACY_POLICY_4: string
  PAYMENT_CONFIRM_DIALOG_PRIVACY_POLICY_5: string
  PAYMENT_CONFIRM_DIALOG_PRIVACY_POLICY_6: string
  PAYMENT_CONFIRM_DIALOG_BACK_TO_CARD_LIST: string
  PAYMENT_CONFIRM_DIALOG_CONFIRM_PAYMENT: string

  PAYMENT_ITEM_INFO_TITLE: string
  PAYMENT_ITEM_INFO_PRODUCT_NAME: string
  PAYMENT_ITEM_INFO_BID_SUCCESS_PRICE: string
  PAYMENT_ITEM_INFO_TAX_INCLUDED_PRICE: string
  PAYMENT_ITEM_INFO_TOTAL_PRICE: string
  PAYMENT_ITEM_INFO_FREE_SHIPPING: string

  PAYMENT_FORM_TITLE: string
  PAYMENT_FORM_SELECT_CARD: string
  PAYMENT_FORM_SELECT_CARD_LABEL: string
  PAYMENT_FORM_NO_CARDS: string
  PAYMENT_FORM_SELECT_CARD_CARD_NO: string
  PAYMENT_FORM_SELECT_CARD_EXPIRE: string
  PAYMENT_FORM_ADD_NEW_CARD: string
  PAYMENT_FORM_ADD_NEW_CARD_MESSAGE: string
  PAYMENT_FORM_NEXT_BUTTON: string

  PAYMENT_RESULT_TITLE: string
  PAYMENT_RESULT_PROCESSING: string
  PAYMENT_RESULT_COMPLETED: string
  PAYMENT_RESULT_PENDING: string
  PAYMENT_RESULT_FAILED: string
  PAYMENT_RESULT_NOT_PROCESSED: string
  PAYMENT_RESULT_COMPLETED_NOTE: string
  PAYMENT_RESULT_BACK_TO_LIST: string
  PAYMENT_RESULT_ACCESS_ID_NOT_FOUND: string
  PAYMENT_RESULT_3DS_ERROR: string
  PAYMENT_RESULT_UNKNOWN_ERROR: string

  // AUCTION & BIDDING SYSTEM
  COMMON_BID_LABEL: string
  BID_COUNT: string
  CLASSIFICATION_ASCENDING: string
  CLASSIFICATION_SEALED: string
  ASCENDING: string
  SEALED: string
  BID_STATUS_INPROGRESS: string
  BID_STATUS_CANCEL: string
  BID_STATUS_NOT_START_YET: string
  BID_STATUS_ENDED: string
  BID_STATUS_PRE_AUCTION: string
  BID_STATUS_EXTENDING: string
  HIGHEST_BIDDER: string
  HIGHEST_BID_AMOUNT: string
  BID_STATUS: string
  REMAINING_TIME: string
  YOU_ARE_TOP: string
  RESERVE_PRICE_NOT_MET: string
  RESERVE_PRICE_MET: string
  DURING_BIDDING_PERIOD: string
  YOU_ARE_NOT_TOP: string
  MORE_LITTLE: string
  SECOND_BIDDER: string
  END_DATE_TIME: string
  START_DATE_TIME: string
  RECORDED_BID_PRICE: string
  DATE_PICKER_DATE_FORMAT: string
  DATE_PICKER_YEAR: string
  DATE_PICKER_MONTH: string
  DATE_PICKER_DAY: string

  // 会員登録画面
  MEMBER_REGISTER_TITLE: string
  MEMBER_REGISTER_SUBTITLE: string
  REGISTER_FORM_REQUIRED: string
  REGISTER_FORM_CONFIRM_BUTTON: string
  REGISTER_FORM_REGISTER_FINISH: string
  REGISTER_SUBTITLE_CONFIRM: string
  PASSWORD_COMPARE_ERROR: string

  // 会員確認画面
  MEMBER_CONFIRM_TITLE: string
  MEMBER_CONFIRM_LOADING: string
  MEMBER_CONFIRM_SUCCESS_TITLE: string
  MEMBER_CONFIRM_SUCCESS_MESSAGE: string
  MEMBER_CONFIRM_ERROR_TITLE: string
  MEMBER_CONFIRM_ERROR_MESSAGE: string
  MEMBER_CONFIRM_TOKEN_NOT_FOUND: string
  MEMBER_CONFIRM_BUTTON: string

  // 会員申請必要書類
  MEMBER_APPLICATION_REQUIRED_DOCUMENTS: string
  MEMBER_APPLICATION_REVIEW_INFO_1: string
  MEMBER_APPLICATION_REVIEW_INFO_2: string
  MEMBER_APPLICATION_ANTIQUE_DEALER_LICENSE: string
  MEMBER_APPLICATION_ANTIQUE_DEALER_TOOLTIP: string
  MEMBER_APPLICATION_EXPORT_MANAGEMENT_NOTICE: string
  MEMBER_APPLICATION_EXPORT_MANAGEMENT_TOOLTIP: string
  MEMBER_APPLICATION_REPRESENTATIVE_BUSINESS_CARD: string
  MEMBER_APPLICATION_REPRESENTATIVE_CARD_TOOLTIP: string
  MEMBER_APPLICATION_SIGNED_DOCUMENT_NOTE: string
  MEMBER_APPLICATION_DOWNLOAD_BUTTON: string
  MEMBER_APPLICATION_UPLOAD_BUTTON: string

  // お知らせ
  NOTICE_BACK_TO_LIST: string
  NOTICE_EMPTY: string

  // Forgot Password
  FORGOT_PASSWORD_CODE_SENT: string
  FORGOT_PASSWORD_USER_NOT_FOUND: string
  FORGOT_PASSWORD_LIMIT_EXCEEDED: string
  FORGOT_PASSWORD_INVALID_PARAMETER: string
  FORGOT_PASSWORD_UNKNOWN_ERROR: string
  FORGOT_PASSWORD_RESET_SUCCESS: string
  FORGOT_PASSWORD_INVALID_CODE: string
  FORGOT_PASSWORD_EXPIRED_CODE: string
  FORGOT_PASSWORD_INVALID_PASSWORD: string

  // Password Reset UI
  LOGIN_REMINDER_REQUEST_ERROR: string
  LOGIN_REMINDER_OTP_SENT_MESSAGE: string
  LOGIN_REMINDER_EMAIL_SENT_TO: string
  LOGIN_REMINDER_OTP_INSTRUCTION: string
  LOGIN_REMINDER_ENTER_OTP: string
  LOGIN_REMINDER_SENDING: string
  LOGIN_REMINDER_EMAIL_PLACEHOLDER: string
  LOGIN_REMINDER_EMAIL_CONFIRM_PLACEHOLDER: string

  PASSWORD_RESET_TITLE: string
  PASSWORD_RESET_SUBTITLE: string
  PASSWORD_RESET_OTP_CODE: string
  PASSWORD_RESET_OTP_PLACEHOLDER: string
  PASSWORD_RESET_NEW_PASSWORD: string
  PASSWORD_RESET_PASSWORD_PLACEHOLDER: string
  PASSWORD_RESET_CONFIRM_PASSWORD: string
  PASSWORD_RESET_SUBMIT: string
  PASSWORD_RESET_PROCESSING: string
  PASSWORD_RESET_SUCCESS_MESSAGE: string
  PASSWORD_RESET_FAILED_MESSAGE: string
  PASSWORD_RESET_UNKNOWN_ERROR: string
  PASSWORD_RESET_EMAIL_MISSING: string
  PASSWORD_RESET_PASSWORD_ERROR: string
  PASSWORD_RESET_CONFIRM_ERROR: string

  PASSWORD_RESET_OTP_TITLE: string
  PASSWORD_RESET_OTP_SUBTITLE: string
  PASSWORD_RESET_OTP_EMAIL_SENT_MESSAGE: string
  PASSWORD_RESET_OTP_ENTER_CODE_INSTRUCTION: string
  PASSWORD_RESET_OTP_VERIFYING: string
  PASSWORD_RESET_OTP_VERIFY: string
  PASSWORD_RESET_OTP_RESEND_CODE: string
  PASSWORD_RESET_OTP_INVALID_CODE: string
  PASSWORD_RESET_OTP_VALID: string
  PASSWORD_RESET_OTP_EXPIRED_CODE: string
  PASSWORD_RESET_RESEND_FAILED: string

  INIT_PASSWORD_TITLE: string
  INIT_PASSWORD_NEW_PASSWORD: string
  INIT_PASSWORD_PASSWORD_PLACEHOLDER: string
  INIT_PASSWORD_PASSWORD_HINT: string
  INIT_PASSWORD_CONFIRM_PASSWORD: string
  INIT_PASSWORD_INPUT_SUBTITLE: string
  INIT_PASSWORD_PROCESSING: string
  INIT_PASSWORD_SUBMIT: string
  INIT_PASSWORD_SUCCESS_MESSAGE: string
  INIT_PASSWORD_FAILED_MESSAGE: string
  INIT_PASSWORD_ALREADY_ACTIVE_MESSAGE: string
  INIT_PASSWORD_UNKNOWN_ERROR: string
  INIT_PASSWORD_PASSWORD_ERROR: string
  INIT_PASSWORD_CONFIRM_ERROR: string
  INIT_PASSWORD_TOKEN_EXPIRED: string
  INIT_PASSWORD_INVALID_ACCESS: string
  INIT_PASSWORD_NO_TOKEN: string
  INIT_PASSWORD_INVALID_TOKEN: string
  INIT_PASSWORD_INVALID_TOKEN_MESSAGE: string
}

export interface Translate {
  ja: TranslationKeys
  en: TranslationKeys
}

// Extract all translation keys as a union type for type-safe t() function
export type TranslationKey = keyof Omit<TranslationKeys, '$vuetify'>

export const translate: Translate = {
  ja: {
    $vuetify: {
      ...jaLocale,
      dataIterator: {
        rowsPerPageText: '表示件数:',
        pageText: '{0}-{1} of {2}',
      },
    },

    // Common translations
    COMMON_BACK: '戻る',
    COMMON_BACK_LIST: '一覧へ戻る',
    COMMON_MORE: 'もっと見る',
    COMMON_JAPAN: '日本',
    COMMON_DUBAI: 'ドバイ',
    COMMON_HONGKONG: '香港',
    COMMON_SEND: '送信する',
    COMMON_AGREE: '同意する',
    COMMON_ERROR: 'エラーが発生しました。',
    COMMON_CONFIRM: '確認',
    COMMON_INPUT_ERROR: '入力エラー',
    COMMON_DATE_FORMAT: 'yyyy年MM月dd日',
    COMMON_REMOVE: '削除',
    COMMON_UPDATE_AUCTION: '更新',
    COMMON_DAY: '日',
    COMMON_HOUR: '時間',
    COMMON_MINUTE: '分',
    COMMON_SECOND: '秒',
    COMMON_MITAIOU: '未対応',
    COMMON_LOADING: '読み込み中',
    COMMON_OK_BUTTON: 'OK',
    ASCENDING_AUCTION: '競り上がり式オークション',
    SEALED_AUCTION: '封印入札式オークション',

    // Site info
    SITE_TITLE: 'AUCTION │ クラウドECオークション',
    COPYRIGHT: '© 2004 GMO MAKESHOP Co. Ltd. All Rights Reserved.',

    // Top page app bar
    TOP_APP_BAR_SELECT_CATEGORY: 'カテゴリを選択',
    TOP_APP_BAR_REGISTER: '新規登録',
    TOP_APP_BAR_LOGIN: 'ログイン',
    TOP_APP_BAR_LOGOUT: 'ログアウト',
    TOP_SEARCH_BY_CATEGORY: 'カテゴリーから探す',
    TOP_SEARCH_ALL_CATEGORIES: 'すべてのカテゴリ',
    TOP_SELECT_CATEGORY: 'カテゴリー選択',
    TOP_UPDATE_INFO: '更新情報',
    TOP_RECOMMEND_PRODUCT: 'おすすめ商品',
    TOP_NEW_PRODUCTS: '新着商品',

    // Top page about section
    TOP_ABOUT_TITLE: 'オークションサイトのパッケージが誕生',
    TOP_ABOUT_SUBTITLE: 'クラウドECオークション',
    TOP_ABOUT_DESCRIPTION_1:
      '「自社オークションを簡単に開催したい」というご要望に応えるパッケージが誕生しました。',
    TOP_ABOUT_DESCRIPTION_2:
      'オークションに必要な基本機能をパッケージ化し、低コストでで導入いただけるサービスです。サイト構築に手間がかからず、比較的短期間での導入が可能です。',
    TOP_ABOUT_DESCRIPTION_3: 'BtoBオークションサイトを実現するカスタマイズにも対応いたします。',
    TOP_ABOUT_DESCRIPTION_4:
      '会員の登録審査機能、お取引先様の管理機能などをご用意しておりますので、お取引先や限定された会員向けのオークションが開催できます。',

    // Top page signup section
    TOP_SIGNUP_SUBTITLE: '登録はいつでも無料',
    TOP_SIGNUP_TITLE: '会員になって、お得に欲しい商品をゲット！',
    TOP_SIGNUP_FREE_SHIPPING_TITLE: '無料配送特典',
    TOP_SIGNUP_FREE_SHIPPING_DESC:
      '会員は、対象商品の配送において、当日配送を除くお届け日時指定便を無料でご利用いただけます。詳細は、',
    TOP_SIGNUP_FREE_SHIPPING_LINK: 'お届け日時指定便について',
    TOP_SIGNUP_FREE_SHIPPING_SUFFIX: 'をご覧ください。',
    TOP_SIGNUP_COUPON_TITLE: 'クーポン配布',
    TOP_SIGNUP_COUPON_DESC: '定期的に割引クーポンを配布しています。クーポンを獲得するには',
    TOP_SIGNUP_COUPON_LOGIN_LINK: 'ログイン',
    TOP_SIGNUP_COUPON_SUFFIX: 'が必要です。',
    TOP_SIGNUP_MEMBER_MORE_BUTTON: '会員についてもっと見る',
    TOP_SIGNUP_REGISTER_BUTTON: '新規会員登録',

    // Header navigation
    HEADER_NAV_PRODUCT_CATEGORY: '商品カテゴリー',
    HEADER_NAV_ALL_CATEGORIES: 'すべてのカテゴリー',
    HEADER_NAV_CATEGORY_1: 'カテゴリー1',
    HEADER_NAV_CATEGORY_2: 'カテゴリー2',
    HEADER_NAV_CATEGORY_3: 'カテゴリー3',
    HEADER_NAV_SEARCH_PLACEHOLDER: '商品名・キーワードで探す',
    HEADER_NAV_SITE_ABOUT: 'サイトについて',
    HEADER_NAV_FIRST_TIME_VISITORS: '初めてご利用の方へ',
    HEADER_NAV_SHOPPING_GUIDE: 'ご利用ガイド',
    HEADER_NAV_FAQ: 'よくあるご質問',
    HEADER_NAV_CONTACT_US: 'お問い合わせ',
    HEADER_NAV_MEMBER_SERVICES: '会員サービス',
    HEADER_NAV_LOGIN: 'ログイン',
    HEADER_NAV_FAVORITES: 'お気に入り',
    HEADER_NAV_BIDDING: '入札中',
    HEADER_NAV_SUCCESSFUL_BID_HISTORY: '落札履歴',
    HEADER_NAV_REGISTER: '新規登録',
    HEADER_NAV_LOGOUT: 'ログアウト',
    HEADER_NAV_NEW_MEMBER_REGISTER: '新規会員登録',
    HEADER_NAV_MEMBER_MENU: '会員メニュー',
    HEADER_NAV_MY_PAGE: 'マイページ',
    HEADER_NAV_GUIDANCE: 'ご案内',
    HEADER_NAV_COMPANY_INFO: '会社情報',
    HEADER_NAV_SERVICE: 'サービス',
    HEADER_NAV_MEMBERSHIP: '会員サービスについて',
    HEADER_NAV_TOKUSHO: '特商法に基づく表記',
    HEADER_NAV_TERMS_OF_SERVICE: '利用規約',
    HEADER_NAV_SPECIFIED_COMMERCIAL_TRANSACTION_LAW: '特定商取引法に基づく表記',
    HEADER_NAV_PRIVACY_POLICY: 'プライバシーポリシー',

    // Footer navigation
    FOOTER_NAV_PRODUCT_PURCHASE: '商品購入',
    FOOTER_NAV_BAGS: 'バッグ',
    FOOTER_NAV_OUTERWEAR: 'アウター',
    FOOTER_NAV_ACCESSORIES: 'アクセサリー',
    FOOTER_NAV_ABOUT_MEMBERSHIP: '会員について',
    FOOTER_NAV_LOGIN: 'ログイン',
    FOOTER_NAV_NEW_MEMBER_REGISTRATION: '新規会員登録',
    FOOTER_NAV_MY_PAGE: 'マイページ',
    FOOTER_NAV_FIRST_TIME_VISITORS: 'はじめての方へ',
    FOOTER_NAV_USAGE_GUIDE: 'ご利用ガイド',
    FOOTER_NAV_FAQ: 'よくあるご質問',
    FOOTER_NAV_MEMBER_SERVICES: '会員サービスについて',
    FOOTER_NAV_GUIDANCE: 'ご案内',
    FOOTER_NAV_NEWS: 'お知らせ',
    FOOTER_NAV_TERMS_OF_SERVICE: '利用規約',
    FOOTER_NAV_COMPANY_OVERVIEW: '会社概要',
    FOOTER_NAV_CAMPAIGN_EARLY_BIRD: 'キャンペーン早割商品',
    FOOTER_NAV_COUPON_PRODUCTS: 'クーポン適用商品',
    FOOTER_NAV_OFFICE_SUPPLIES: 'オフィス・事務用品',
    FOOTER_NAV_COMPUTERS: 'コンピュータ',
    FOOTER_NAV_AV_ELECTRONICS: 'AV機器・家電',
    FOOTER_NAV_NEW_MEMBER_DISCOUNT: '新規会員限定割引',
    FOOTER_NAV_STORE_FIXTURES: '店舗什器・備品',
    FOOTER_NAV_CONSTRUCTION_MATERIALS: '建築資材・廃材',
    FOOTER_NAV_COMPANY_GUIDE: '会社案内',
    FOOTER_NAV_PRIVACY_POLICY: 'プライバシーポリシー',
    FOOTER_NAV_COMMERCIAL_TRANSACTION_LAW: '特定商取引法に基づく表記',

    ROUTE_TOP: 'トップ',

    // Product detail
    DETAIL_TITLE: '商品詳細',
    DETAIL_DESCRIPTION: '商品説明',
    DETAIL_CURRENCY: '円',
    DETAIL_QUANTITY: '出品数量',
    DETAIL_LOWEST_BID_QUANTITY: '最低入札数量',
    DETAIL_LOWEST_BID_PRICE: '最低入札単価',
    DETAIL_BID_COUNT: '入札件数',
    DETAIL_BID_QUANTITY: '入札数量',
    DETAIL_BID_UNIT_PRICE: '入札単価',
    NYUSATSU_KAKAKU: '入札価格',
    DETAIL_BID_TOTAL_PRICE: '入札合計金額',
    DETAIL_BID_BUTTON: '入札する',
    DETAIL_CONTACT_BUTTON: 'この商品に関するお問い合わせ',
    DETAIL_ABOUT_RANK: '商品状態ランクについて',
    DETAIL_CHAT: 'チャットで質問する',
    DETAIL_VIEW_COMMENTS: '質問コメントを見る',
    DETAIL_CANCEL_BID: '入札をキャンセルする',
    DETAIL_BID_HISTORY: '入札履歴',
    DETAIL_LAST_BID_TIME: '最終入札時刻',
    DETAIL_LAST_BID_AMOUNT: '入札額',
    DETAIL_HIGHEST_BID_AMOUNT: '最高入札額',
    DETAIL_BACK_TO_LIST: '一覧に戻る',

    // Product detail info
    DETAIL_INFO_MAKER: 'メーカー',
    DETAIL_INFO_PRODUCT_NAME: '商品名',
    DETAIL_INFO_SIM: 'SIM',
    DETAIL_INFO_CAPACITY: '容量',
    DETAIL_INFO_COLOR: '色',
    DETAIL_INFO_RANK: 'グレード',
    DETAIL_INFO_QUANTITY: '数量',
    DETAIL_INFO_NOTE1: '備考1',
    DETAIL_INFO_NOTE2: '備考2',
    DETAIL_INFO_LOWEST_BID_PRICE: '最低入札<br>単価',
    DETAIL_INFO_LOWEST_BID_QUANTITY: '最低入札<br>数量',
    DETAIL_INFO_FAVORITE: 'お気に入り',
    DETAIL_INFO_START_PRICE: 'スタート価格',
    DETAIL_INFO_CURRENT_PRICE: '現在価格',
    DETAIL_INFO_TAX_INCLUDED_PRICE: '税込価格',
    DETAIL_DEAL_BID_PRICE: '即決価格',
    DETAIL_BID_PERIOD: '入札期間',

    // Error messages（エラーメッセージ）
    ERROR_PRICE_EMPTY: '入札価格を入力してください。',
    ERROR_PRICE_INVALID_FORMAT: '入札価格は数字のみを入力してください。',
    ERROR_PRICE_MAX_LENGTH: '整数部は{0}桁以内、小数点以下は{1}桁以内で入力してください。',
    ERROR_LOWEST_UNIT_PRICE_INVALID: '最低入札価格以上の数値を入力してください。',
    ERROR_NEW_PRICE_LOWER_THAN_CURRENT_PRICE_ERR: '現在価格以下の入札はできません。',
    ERROR_QUANTITY_EMPTY: '入札数量を入力してください。',
    ERROR_QUANTITY_INVALID_FORMAT: '入札数量は数字のみを入力してください。',
    ERROR_QUANTITY_MAX_LENGTH: '入札数量は{0}桁以下の数字で入力してください。',
    ERROR_LOWEST_BID_QUANTITY_INVALID: '最低入札数量以上の数値を入力してください。',
    ERROR_BID_QUANTITY_EXCEEDS_MAX: '入札数量が出品数量を超えています。',
    ERROR_BID_MODAL_LOGIN_REQUIRED_MESSAGE: '会員限定で入札できます。ログインしてください。',

    // Bid Modal
    BID_MODAL_LOGIN_REQUIRED: '会員限定で入札できます。ログインしてください。',
    BID_MODAL_BID_CANCELLED_SUCCESS: '入札をキャンセルしました。',
    BID_MODAL_CONFIRM_MESSAGE: 'お間違いなければ入札ボタンをクリックしてください。',
    BID_MODAL_CANCEL_CONFIRMATION: '入札をキャンセルしますか？',
    BID_MODAL_CONFIRM_BID: '入札確定する',
    BID_VALIDATION_ERROR: '入札検証エラーが発生しました。',
    BID_ERROR_OCCURRED: '入札処理中にエラーが発生しました。',
    BID_SUCCESS_MESSAGE: '入札を受け付けました。',

    SEARCH_RESULT: '検索結果',
    PRODUCT_LIST: '商品一覧',

    // Filter box
    FILTER_BOX_TITLE: '検索条件',
    FILTER_BOX_CATEGORY: 'カテゴリ',

    // Search functionality
    FILTER_BOX_SEARCH_ACTION: '検索する',
    FILTER_BOX_CLEAR_CONDITIONS_ACTION: '検索条件のクリア',
    FILTER_BOX_KEYWORD_FIELD: 'キーワード',
    FILTER_BOX_PRODUCT_SEARCH: '商品検索',
    FILTER_BOX_SEARCH_PLACEHOLDER: '商品名・キーワードで探す',
    FILTER_BOX_COUNT_UNIT: '件',

    // Search results page
    SEARCH_RESULTS_SORT_RECOMMENDED: 'おすすめ順',
    SEARCH_RESULTS_SORT_NEWEST: '新着順',
    SEARCH_RESULTS_SORT_TIME_REMAINING_ASC: '残り時間の少ない順',
    SEARCH_RESULTS_SORT_TIME_REMAINING_DESC: '残り時間の長い順',
    SEARCH_RESULTS_SORT_PRICE_ASC: '現在価格の安い順',
    SEARCH_RESULTS_SORT_PRICE_DESC: '現在価格の高い順',
    SEARCH_RESULTS_SORT_BID_COUNT_DESC: '入札件数の多い順',
    SEARCH_RESULTS_SORT_BID_COUNT_ASC: '入札件数の少ない順',
    SEARCH_RESULTS_ON_SALE_ONLY: '販売中のみ',
    SEARCH_RESULTS_DOWNLOAD_CSV_PRODUCT_INFO: '商品情報ダウンロード（CSV）',
    SEARCH_RESULTS_DOWNLOAD_CSV_PRODUCT_HANDOVER: '商品情報ダウンロード（CSV）',
    SEARCH_RESULTS_DISPLAY_COUNT: '表示件数',
    SEARCH_RESULTS_20_ITEMS: '20件',
    SEARCH_RESULTS_50_ITEMS: '50件',
    SEARCH_RESULTS_100_ITEMS: '100件',
    SEARCH_RESULTS_PAGINATION_OF: '件中',
    SEARCH_RESULTS_PAGINATION_DISPLAYING: '件を表示',

    MYPAGE_AUCTION_COUNT: '件のオークション',

    // Product list
    LIST_CURRENT: '現在',
    LIST_LOWEST_BID_PRICE: '最低入札価格',
    LIST_END_DATE_PLAN: '終了予定日時',

    // Favorite
    FAVORITE_TITLE: 'お気に入り',
    FAVORITE_EMPTY: 'お気に入りに商品がありません。',
    FAVORITE_CLEAR_PRICE_INPUT: '入力値クリア',
    FAVORITE_SUB_TOTAL_BID_PRICE_HEADER: '入札小計',
    FAVORITE_SUB_TOTAL_BID_PRICE: '入札小計',
    FAVORITE_LOGIN_REQUIRED_FAVORITE: 'ログイン後にお気に入りリストに入れられます。',
    FAVORITE_BID_BUTTON: '入札する',
    FAVORITE_RE_BID_BUTTON: '再入札する',
    FAVORITE_BID_QUANTITY: '入札数量',
    FAVORITE_DELETE_FAVORITE1: 'お気に入り',
    FAVORITE_DELETE_FAVORITE2: 'から削除',

    // Bid history
    BID_HISTORY_END_DATE: '落札日',
    BID_HISTORY_BID_SUCCESS_UNIT_PRICE: '落札単価',
    BID_HISTORY_BID_SUCCESS_PRICE: '落札価格',
    BID_HISTORY_BID_SUCCESS_QUANTITY: '落札数',
    BID_HISTORY_BID_TOTAL_PRICE: '合計金額',
    BID_HISTORY_INVOICE_DOWNLOAD: '請求書ダウンロード',
    BID_HISTORY_PAYMENT_STATUS_PAID: '支払済み',
    BID_HISTORY_PAYMENT_STATUS_PENDING: '処理中',
    BID_HISTORY_PAYMENT_STATUS_UNPAID: 'お支払い手続き',
    BID_HISTORY_CURRENT_PRICE: '現在',
    BID_HISTORY_ENDED: '終了',

    // Chat
    INQUIRY_CHAT: 'お問い合わせチャット',
    CHAT_BACK_ITEM: '商品に戻る',
    CHAT_CHAT_ROOM: 'チャットルーム',
    CHAT_SHOP: 'ショップ',
    CHAT_HIDDEN_DESCRIPTION: '投稿内容がポリシーに反すると判断されたため、非表示としています。',
    CHAT_INPUT_PLACEHOLDER: 'メッセージを入力する',
    CHAT_SEND_BUTTON: 'メッセージを投稿する',
    CHAT_CLEAR_BUTTON: 'クリア',
    CHAT_ATTENTION_1: '※名前やメールアドレスなどの個人情報を入力しないようご注意ください。',
    CHAT_ATTENTION_2: '※投稿内容は他のユーザーにも公開されます。',
    CHAT_ATTENTION_3:
      '※送信する前に入力内容をよく確認してください。一度掲載された投稿内容は、編集および削除できません。',
    ERROR_CHAT_LOGIN_REQUIRED_MESSAGE: '会員限定でお問い合わせできます。ログインしてください。',

    // MY Page Member Edit
    MYPAGE_MEMBER_EDIT_LICENSE_COPY: '貴社古物商許可書のコピー',
    MYPAGE_MEMBER_EDIT_LICENSE_NOTES: '貴社ご署名入りの弊社輸出管理に関する留意事項',
    MYPAGE_MEMBER_EDIT_LICENSE_NOTES1: '※署名捺印した本紙はご郵送ください。',
    MYPAGE_MEMBER_EDIT_BUSINESS_CARD_COPY: '代表者様の名刺のコピー',
    MYPAGE_MEMBER_EDIT_DOWNLOAD: '申請必要書類をダウンロード',
    MYPAGE_MEMBER_EDIT_CONFIRM_BUTTON: '入力内容を確認する',
    MYPAGE_MEMBER_EDIT_WITHDRAW_BUTTON: '退会する',
    MYPAGE_MEMBER_EDIT_REQUIRED_LABEL: '※必須',
    MYPAGE_MEMBER_EDIT_UPLOAD_LABEL: 'アップロード',
    MYPAGE_MEMBER_EDIT_COMPLETE_MESSAGE: '会員情報を更新しました。',
    MYPAGE_MEMBER_EDIT_CONFIRM: '入力内容の確認',

    // Auth
    AUTH_LOGOUT_MESSAGE: 'ログアウトしますか？',
    AUTH_LOGOUT: 'ログアウト',
    AUTH_CLOSE: '閉じる',
    AUTH_CANCEL: 'キャンセル',

    // Cognito standardized error messages
    AUTH_COGNITO_USERNAME_EXISTS_EXCEPTION: 'このメールアドレスは既に登録されています。',
    AUTH_COGNITO_INVALID_PASSWORD_EXCEPTION: 'パスワードはポリシーの要件を満たしていません。',
    AUTH_COGNITO_INVALID_PARAMETER_EXCEPTION: '入力内容が正しくありません。',
    AUTH_COGNITO_CODE_MISMATCH_EXCEPTION: '認証コードが正しくありません。',
    AUTH_COGNITO_LIMIT_EXCEEDED_EXCEPTION:
      'リクエスト回数が制限を超えました。しばらくしてから再度お試しください。',
    AUTH_COGNITO_EXPIRED_CODE_EXCEPTION: '認証コードの有効期限が切れています。',
    AUTH_COGNITO_NOT_AUTHORIZED_EXCEPTION: '認証に失敗しました。',
    AUTH_COGNITO_CODE_DELIVERY_FAILURE_EXCEPTION: '認証コードの送信に失敗しました。',
    AUTH_COGNITO_USER_NOT_CONFIRMED_EXCEPTION: 'このアカウントは有効性が検証されていません。',
    AUTH_COGNITO_PASSWORD_RESET_REQUIRED_EXCEPTION: 'パスワードのリセットが必要です。',
    AUTH_COGNITO_USER_NOT_FOUND_EXCEPTION: 'このメールアドレスは登録されていません。',
    AUTH_COGNITO_COGNITO_UNKNOWN_ERROR: '不明なエラーが発生しました。',

    // Login
    LOGIN_TITLE: 'ログイン',
    LOGIN_SUBTITLE: 'login',
    LOGIN_ID: 'ログインID',
    LOGIN_EMAIL: 'メールアドレス',
    LOGIN_EMAIL_CONFIRM: 'メールアドレス(確認用)',
    LOGIN_PASSWORD: 'パスワード',
    LOGIN_REQUIRED: '※必須',
    LOGIN_PLACEHOLDER_ID_PASSWORD: '8～14文字の半角英数字',
    LOGIN_SAVE_ID_PASSWORD: 'ID・パスワードを保存',
    LOGIN_SAVE_LOGIN_INFO: 'メールアドレス・パスワードを保存',
    LOGIN_FORGET_PASSWORD: 'パスワードを忘れた方はコチラ',
    LOGIN_FORGET_PASSWORD_LINK: 'パスワードをお忘れの方',
    LOGIN_RULE: '参加規約',
    LOGIN_RULE_TITLE: '入札会参加要項',
    LOGIN_AGREE_RULE: '参加規約に同意する',
    LOGIN_AGREE: '同意する',
    LOGIN_ENTRY_INFO1: '新規会員登録(無料)',
    LOGIN_ENTRY_INFO2: '商品への入札は会員登録が必要です。',
    LOGIN_NEW_MEMBER_REGISTER: '新規会員登録',
    LOGIN_PRICE_VIEW_REQUIRES_REGISTRATION: '※商品の価格を見るには会員登録が必要です。',
    LOGIN_CONFIRM_BUTTON: 'ログイン',
    LOGIN_PROCESSING: '処理中...',
    LOGIN_PASSWORD_HINT: '8～16文字の半角英数字',
    LOGIN_VALIDATION_NOT_ENTERED: '未入力です',
    LOGIN_VALIDATION_PASSWORD_LENGTH: 'パスワードは8～14文字で入力してください',
    LOGIN_VALIDATION_AGREE_TERMS: '参加規約に同意してください',
    LOGIN_ERROR_INVALID_CREDENTIALS: 'ログインIDまたはパスワードが正しくありません。',
    LOGIN_ERROR_INVALID_OTP_CODE: '認証コードが正しくありません。正しいコードを入力してください。',
    LOGIN_ERROR_OTP_SESSION_EXPIRED:
      'セッションが無効になった可能性があります。もう一度ログインしてください。',
    LOGIN_ERROR_LOGIN_FAILED: 'ログインに失敗しました。入力情報を確認してください。',
    LOGIN_ERROR_NEW_PASSWORD_REQUIRED: '初回ログイン時はパスワードの変更が必要です。',
    LOGIN_ERROR_UNKNOWN: '不明なエラーが発生しました。',
    LOGIN_ERROR_USER_NOT_CONFIRMED: 'ログインIDまたはパスワードが正しくありません。',
    LOGIN_ERROR_USER_ALREADY_AUTHENTICATED: '既にログインしています。',
    LOGIN_ERROR_LIMIT_EXCEEDED:
      'ログイン試行回数が制限を超えました。しばらくしてから再度お試しください。',
    LOGIN_ERROR_INVALID_PASSWORD: 'パスワードはポリシーの要件を満たしていません。',
    LOGIN_ERROR_TOO_MANY_REQUESTS: 'リクエストが多すぎます。しばらくしてから再度お試しください。',
    LOGIN_ERROR_LOGIN_HISTORY_FAILED: 'ログイン履歴の記録に失敗しました。',
    LOGIN_ERROR_EMAIL_VERIFICATION_REQUIRED: 'メールアドレスの確認が必要です。',
    LOGIN_ERROR_EMAIL_CODE_REQUIRED: 'メール認証コードの入力が必要です。',
    LOGIN_ERROR_UNSUPPORTED_STEP: '未対応の認証ステップです:',
    LOGIN_ERROR_MEMBER_STATUS_FAILED: '会員ステータスの取得に失敗しました。',
    LOGIN_ERROR_LOGIN_FAILED_BY_STATUS: '停止または退会済みの会員のためログインできません。',

    // OTP (One-Time Password) Page
    OTP_TITLE: '認証コードを入力',
    OTP_SUBTITLE: 'Enter your verification code',
    OTP_EMAIL_SENT_MESSAGE: 'ご登録のメールアドレスに認証コードを送信しました。',
    OTP_ENTER_CODE_INSTRUCTION: '6桁の認証コードを入力してください。',
    OTP_CODE_GROUP_LABEL: '6桁の認証コード',
    OTP_DIGIT_SUFFIX: '桁目',
    OTP_AUTHENTICATING: '認証中...',
    OTP_AUTHENTICATE: '認証する',
    OTP_RESEND_CODE: '認証コードを再送する',
    OTP_AUTHENTICATION_FAILED: '認証に失敗しました。',
    OTP_INVALID_CODE: '認証コードが正しくありません。',
    OTP_RESEND_IN_DEVELOPMENT: '認証コード再送機能は現在実装中です。',

    // Password Reminder
    LOGIN_REMINDER_TITLE: 'パスワードをお忘れの方',
    LOGIN_REMINDER_SUBTITLE: 'Reminder',
    LOGIN_REMINDER_MESSAGE1: 'パスワードをお忘れの方は登録したメールアドレスを入力してください。',
    LOGIN_REMINDER_MESSAGE2:
      '「送信」ボタンを押しますと、パスワードリセット用の認証コードが登録メールアドレスに届きます。',
    LOGIN_REMINDER_USER_ID: 'ID',
    LOGIN_REMINDER_USER_ID_PLACEHOLDER: '半角英数字',
    LOGIN_REMINDER_SEND_BUTTON: '送信',
    LOGIN_REMINDER_COMPLETE_MESSAGE:
      'パスワードをリセットしました。ログイン画面からログインしてください。',
    LOGIN_REMINDER_BACK_TO_LOGIN: 'ログイン画面へ戻る',
    LOGIN_REMINDER_EMAIL_ERROR: 'メールアドレスが正しくありません',
    LOGIN_REMINDER_CONFIRM_EMAIL_ERROR: 'メールアドレスが一致しません',

    MYPAGE_EDIT_PROFILE: '会員情報編集',
    MYPAGE_EDIT_CONFIRM: '会員情報編集確認',
    MYPAGE_CARD: 'カード情報',

    // Payment Form
    PAYMENT_CURRENT_INFO: '現在の支払い情報',
    PAYMENT_COMPLETED: '支払いは完了しています。',
    PAYMENT_PENDING: '支払いが保留中です。後ほどもう一度ご確認ください。',
    PAYMENT_FAILED: '支払いに失敗しました。別のカードでお試しください。',
    PAYMENT_NOT_PROCESSED: 'まだ支払いが処理されていません。後ほどもう一度ご確認ください。',
    CARD_LIST: '登録済みクレジットカード',
    CARD_ADD: '新しいクレジットカードを追加',
    CARD_SET_DEFAULT: 'デフォルトに設定',
    CARD_DELETE: '削除',
    PAYMENT_BUTTON: '支払い',
    PAYMENT_COMPLETE: 'お支払い手続き完了',
    PAYMENT_COMPLETE_MESSAGE: 'お支払い手続きが完了しました。',
    PAYMENT_FAIL: 'お支払い手続き失敗',
    PAYMENT_FAIL_MESSAGE: 'お支払い手続きに失敗しました。',
    CARD_DEFAULT_CHANGE_COMPLETE: 'デフォルトカード変更完了',
    CARD_DEFAULT_CHANGE_MESSAGE: 'デフォルトカードが変更されました。',
    CARD_DELETE_COMPLETE: 'カード削除完了',
    CARD_DELETE_MESSAGE: 'カードが削除されました。',
    CARD_EDIT: '編集',
    CARD_NO_DATA: '現在登録されているクレジットカードはありません。',
    CARD_NO: 'カード番号',
    CARD_DEFAULT: '通常使うカード',

    CARD_INPUT_REQUIRED: '必須',
    CARD_HOLDER_NAME: 'カード名義',
    CARD_HOLDER_NAME_PLACEHOLDER: '例）TARO YAMADA',
    CARD_NUMBER: 'カード番号',
    CARD_NUMBER_PLACEHOLDER: '例）1234-5678-XXXX-XXXX',
    CARD_EXPIRATION: '有効期限',
    CARD_EXPIRATION_MONTH: '月',
    CARD_EXPIRATION_MONTH_PLACEHOLDER: '例）05',
    CARD_EXPIRATION_YEAR: '年',
    CARD_EXPIRATION_YEAR_PLACEHOLDER: '例）25',
    CARD_SECURITY_CODE: 'セキュリティコード',
    CARD_SECURITY_CODE_PLACEHOLDER: '例）123',
    CARD_DEFAULT_SETTING: '利用設定',
    CARD_DEFAULT_SETTING_TEXT: '通常利用に指定する',

    CARD_INPUT_HALF_WIDTH: '半角英字',
    CARD_COMPLETE_DIALOG_CLOSE: '閉じる',

    CARD_CONFIRM_DIALOG_DEFAULT_MESSAGE: '以下のカードをデフォルトにしてもよろしいですか。',
    CARD_CONFIRM_DIALOG_DELETE_MESSAGE: '以下のカードを削除してもよろしいですか。',
    CARD_CONFIRM_DIALOG_CLOSE: '閉じる',
    CARD_CONFIRM_DIALOG_UPDATE: '変更する',
    CARD_CONFIRM_DIALOG_DELETE: '削除する',

    CARD_EDIT_DIALOG_TITLE: 'クレジットカード情報の編集',
    CARD_EDIT_DIALOG_CARD_NO: 'カード番号',
    CARD_EDIT_DIALOG_EXPIRE: '有効期限',
    CARD_EDIT_DIALOG_SECURITY_CODE: 'セキュリティコード',
    CARD_EDIT_DIALOG_USE_SETTING: '利用設定',
    CARD_EDIT_DIALOG_USE_SETTING_TEXT: '通常利用に指定する',
    CARD_EDIT_DIALOG_CONFIRM: '入力内容を確認する',

    PAYMENT_COMPLETE_DIALOG_TITLE: 'お支払い手続き完了',
    PAYMENT_COMPLETE_DIALOG_MESSAGE: 'お支払い手続きが完了しました。',
    PAYMENT_COMPLETE_DIALOG_NOTE:
      '※３営業日以内にショップからご連絡のメールが届きます。詳細はメールにてご確認いただけます。',
    PAYMENT_COMPLETE_DIALOG_BACK_TO_LIST: '落札履歴に戻る',

    PAYMENT_CONFIRM_DIALOG_TITLE: 'お支払い方法',
    PAYMENT_CONFIRM_DIALOG_MESSAGE: 'この内容でお支払い手続きを進めますか？',
    PAYMENT_CONFIRM_DIALOG_PROCESSING: 'お支払い処理中です。しばらくお待ちください',
    PAYMENT_CONFIRM_DIALOG_CREDIT_CARD: 'クレジットカード',
    PAYMENT_CONFIRM_DIALOG_CREDIT_CARD_1TIME: '（一括払い）',
    PAYMENT_CONFIRM_DIALOG_PRIVACY_POLICY_1: '※お支払いは',
    PAYMENT_CONFIRM_DIALOG_PRIVACY_POLICY_2: '個人情報保護方針',
    PAYMENT_CONFIRM_DIALOG_PRIVACY_POLICY_3: 'と',
    PAYMENT_CONFIRM_DIALOG_PRIVACY_POLICY_4: '利用規約',
    PAYMENT_CONFIRM_DIALOG_PRIVACY_POLICY_5: 'に同意して注文したことになります。',
    PAYMENT_CONFIRM_DIALOG_PRIVACY_POLICY_6:
      '※お客様と異なる名義のクレジットカードはご利用いただけません。',
    PAYMENT_CONFIRM_DIALOG_BACK_TO_CARD_LIST: 'クレジットカードの選択へ戻る',
    PAYMENT_CONFIRM_DIALOG_CONFIRM_PAYMENT: '支払いを確定する',

    PAYMENT_ITEM_INFO_TITLE: '落札商品',
    PAYMENT_ITEM_INFO_PRODUCT_NAME: '商品名',
    PAYMENT_ITEM_INFO_BID_SUCCESS_PRICE: '落札価格',
    PAYMENT_ITEM_INFO_TAX_INCLUDED_PRICE: '税込価格',
    PAYMENT_ITEM_INFO_TOTAL_PRICE: '支払い金額',
    PAYMENT_ITEM_INFO_FREE_SHIPPING: '送料無料',

    PAYMENT_FORM_TITLE: 'お支払い方法',
    PAYMENT_FORM_SELECT_CARD: '登録済みのカードを利用する',
    PAYMENT_FORM_SELECT_CARD_LABEL: 'カードを選択',
    PAYMENT_FORM_NO_CARDS: '現在登録されているクレジットカードはありません。',
    PAYMENT_FORM_SELECT_CARD_CARD_NO: 'カード番号',
    PAYMENT_FORM_SELECT_CARD_EXPIRE: '有効期限',
    PAYMENT_FORM_ADD_NEW_CARD: '新しいクレジットカードを利用する',
    PAYMENT_FORM_ADD_NEW_CARD_MESSAGE:
      '※今回利用するカードはマイページのお支払い方法に登録されます。',
    PAYMENT_FORM_NEXT_BUTTON: '支払い内容を確認する',

    PAYMENT_RESULT_TITLE: 'お支払い結果',
    PAYMENT_RESULT_PROCESSING: 'お支払い処理中です。しばらくお待ちください...',
    PAYMENT_RESULT_COMPLETED: 'お支払い手続きが完了しました。',
    PAYMENT_RESULT_PENDING: 'お支払いが保留中です。後ほどもう一度ご確認ください。',
    PAYMENT_RESULT_FAILED: 'お支払いに失敗しました。別のカードでお試しください。',
    PAYMENT_RESULT_NOT_PROCESSED:
      'まだお支払いが処理されていません。後ほどもう一度ご確認ください。',
    PAYMENT_RESULT_COMPLETED_NOTE:
      '※３営業日以内にショップからご連絡のメールが届きます。詳細はメールにてご確認いただけます。',
    PAYMENT_RESULT_BACK_TO_LIST: '落札履歴に戻る',
    PAYMENT_RESULT_ACCESS_ID_NOT_FOUND: '取引IDが見つかりません。',
    PAYMENT_RESULT_3DS_ERROR: '3DS2.0認証後決済実行が失敗しました。',
    PAYMENT_RESULT_UNKNOWN_ERROR: '支払い状態の取得に失敗しました。',

    // Existing uppercase keys (keep as-is)
    COMMON_BID_LABEL: '入札',
    BID_COUNT: '件',
    CLASSIFICATION_ASCENDING: '競り上がり入札',
    CLASSIFICATION_SEALED: '封印入札',
    ASCENDING: '競り上がり入札',
    SEALED: '封印入札',
    BID_STATUS_INPROGRESS: '入札受付中',
    BID_STATUS_CANCEL: '出品停止',
    BID_STATUS_NOT_START_YET: '開始待ち',
    BID_STATUS_ENDED: 'オークション終了',
    BID_STATUS_PRE_AUCTION: '下見期間中',
    BID_STATUS_EXTENDING: 'オークション延長中',
    DURING_BIDDING_PERIOD: '入札期間中',
    HIGHEST_BIDDER: '最高入札者',
    HIGHEST_BID_AMOUNT: '最高入札額',
    BID_STATUS: 'ステータス',
    REMAINING_TIME: '残り時間',
    YOU_ARE_TOP: 'あなたがTOP',
    RESERVE_PRICE_NOT_MET: '最低落札価格に達していません',
    RESERVE_PRICE_MET: '最低落札価格に達しました',
    YOU_ARE_NOT_TOP: 'あなたはTOP入札者ではありません',
    MORE_LITTLE: 'もう少しで最低落札価格です',
    SECOND_BIDDER: '2位入札者',
    END_DATE_TIME: '終了日時',
    START_DATE_TIME: '開始日時',
    RECORDED_BID_PRICE: '入札済み価格',

    // Date picker
    DATE_PICKER_DATE_FORMAT: 'yyyy年MM月dd日',
    DATE_PICKER_YEAR: '年',
    DATE_PICKER_MONTH: '月',
    DATE_PICKER_DAY: '日',

    UNIT_BID_AVAILABLE: '単位で入札できます',
    SCHEDULED_END: '終了予定',
    SHARE_THIS_PRODUCT: 'この商品をシェア',

    // 会員登録画面
    MEMBER_REGISTER_TITLE: '会員登録',
    MEMBER_REGISTER_SUBTITLE: '会員登録',
    REGISTER_FORM_REQUIRED: '※必須',
    REGISTER_FORM_CONFIRM_BUTTON: '入力内容を確認する',
    REGISTER_FORM_REGISTER_FINISH: '登録完了しました。',
    REGISTER_SUBTITLE_CONFIRM: '内容確認',
    PASSWORD_COMPARE_ERROR: 'パスワードと確認用パスワードが一致しません。',

    // 会員確認画面
    MEMBER_CONFIRM_TITLE: '会員確認',
    MEMBER_CONFIRM_LOADING: '確認中...',
    MEMBER_CONFIRM_SUCCESS_TITLE: '確認完了！',
    MEMBER_CONFIRM_SUCCESS_MESSAGE: 'アカウントの確認が正常に完了しました。',
    MEMBER_CONFIRM_ERROR_TITLE: 'エラー',
    MEMBER_CONFIRM_ERROR_MESSAGE: '確認中にエラーが発生しました。',
    MEMBER_CONFIRM_TOKEN_NOT_FOUND: '確認トークンが見つかりません。',
    MEMBER_CONFIRM_BUTTON: 'OK',

    // 会員申請必要書類
    MEMBER_APPLICATION_REQUIRED_DOCUMENTS: '会員申請必要書類',
    MEMBER_APPLICATION_REVIEW_INFO_1: '会員ご登録申請にあたり当社にて審査を行っています。',
    MEMBER_APPLICATION_REVIEW_INFO_2: '以下より必要書類をダウンロードいただけます。',
    MEMBER_APPLICATION_ANTIQUE_DEALER_LICENSE: '古物商許可証のコピー',
    MEMBER_APPLICATION_ANTIQUE_DEALER_TOOLTIP:
      '古物商許可証の番号が確認できるようにコピーしてください。',
    MEMBER_APPLICATION_EXPORT_MANAGEMENT_NOTICE: '貴社ご署名入りの弊社輸出管理に関する留意事項',
    MEMBER_APPLICATION_EXPORT_MANAGEMENT_TOOLTIP: '署名捺印が必要です。',
    MEMBER_APPLICATION_REPRESENTATIVE_BUSINESS_CARD: '代表者様の名刺のコピー',
    MEMBER_APPLICATION_REPRESENTATIVE_CARD_TOOLTIP: '名刺は表面のみご用意ください。',
    MEMBER_APPLICATION_SIGNED_DOCUMENT_NOTE: '※署名捺印した本紙はご郵送ください。',
    MEMBER_APPLICATION_DOWNLOAD_BUTTON: '申請必要書類をダウンロード',
    MEMBER_APPLICATION_UPLOAD_BUTTON: 'アップロード',

    // お知らせ
    NOTICE_BACK_TO_LIST: 'お知らせ一覧へ戻る',
    NOTICE_EMPTY: 'お知らせがありません。',

    // Forgot Password
    FORGOT_PASSWORD_CODE_SENT: 'パスワードリセット用の認証コードを送信しました。',
    FORGOT_PASSWORD_USER_NOT_FOUND: 'このメールアドレスは登録されていません。',
    FORGOT_PASSWORD_LIMIT_EXCEEDED:
      'リクエストが制限を超えました。しばらくしてから再度お試しください。',
    FORGOT_PASSWORD_INVALID_PARAMETER: 'パラメータが無効です。',
    FORGOT_PASSWORD_UNKNOWN_ERROR: '不明なエラーが発生しました。',
    FORGOT_PASSWORD_RESET_SUCCESS: 'パスワードが正常にリセットされました。',
    FORGOT_PASSWORD_INVALID_CODE: '認証コードが正しくありません。',
    FORGOT_PASSWORD_EXPIRED_CODE: '認証コードの有効期限が切れています。',
    FORGOT_PASSWORD_INVALID_PASSWORD: 'パスワードがポリシーの要件を満たしていません。',

    // Password Reset UI
    LOGIN_REMINDER_REQUEST_ERROR: 'パスワードリセットの要求に失敗しました。',
    LOGIN_REMINDER_OTP_SENT_MESSAGE: 'パスワードリセット用の認証コードをメールで送信しました。',
    LOGIN_REMINDER_EMAIL_SENT_TO: '送信先',
    LOGIN_REMINDER_OTP_INSTRUCTION: 'メールに記載された6桁の認証コードを入力してください。',
    LOGIN_REMINDER_ENTER_OTP: '認証コードを入力',
    LOGIN_REMINDER_SENDING: '送信中...',
    LOGIN_REMINDER_EMAIL_PLACEHOLDER: 'メールアドレスを入力してください',
    LOGIN_REMINDER_EMAIL_CONFIRM_PLACEHOLDER: 'メールアドレスを再入力してください',

    PASSWORD_RESET_TITLE: 'パスワードを入力',
    PASSWORD_RESET_SUBTITLE: 'Password',
    PASSWORD_RESET_OTP_CODE: '認証コード',
    PASSWORD_RESET_OTP_PLACEHOLDER: '6桁の認証コードを入力',
    PASSWORD_RESET_NEW_PASSWORD: 'パスワード',
    PASSWORD_RESET_PASSWORD_PLACEHOLDER: '8～14文字の半角英数字',
    PASSWORD_RESET_CONFIRM_PASSWORD: 'パスワード（確認用）',
    PASSWORD_RESET_SUBMIT: 'パスワードをリセット',
    PASSWORD_RESET_PROCESSING: '処理中...',
    PASSWORD_RESET_SUCCESS_MESSAGE:
      'パスワードが正常にリセットされました。新しいパスワードでログインしてください。',
    PASSWORD_RESET_FAILED_MESSAGE: 'パスワードのリセットに失敗しました。',
    PASSWORD_RESET_UNKNOWN_ERROR: '不明なエラーが発生しました。',
    PASSWORD_RESET_EMAIL_MISSING: 'メールアドレスが設定されていません。',
    PASSWORD_RESET_PASSWORD_ERROR: 'パスワードの形式が正しくありません。',
    PASSWORD_RESET_CONFIRM_ERROR: 'パスワードが一致しません。',

    PASSWORD_RESET_OTP_TITLE: '認証コード入力',
    PASSWORD_RESET_OTP_SUBTITLE: '認証コードを入力してください',
    PASSWORD_RESET_OTP_EMAIL_SENT_MESSAGE:
      'パスワードリセット用の認証コードをメールで送信しました。',
    PASSWORD_RESET_OTP_ENTER_CODE_INSTRUCTION:
      'メールに記載された6桁の認証コードを入力してください。',
    PASSWORD_RESET_OTP_VERIFYING: '認証中...',
    PASSWORD_RESET_OTP_VERIFY: '認証する',
    PASSWORD_RESET_OTP_RESEND_CODE: '認証コードを再送信',
    PASSWORD_RESET_OTP_INVALID_CODE: '認証コードが正しくありません。',
    PASSWORD_RESET_OTP_VALID: '認証コードが正しく確認されました。',
    PASSWORD_RESET_OTP_EXPIRED_CODE: '認証コードの有効期限が切れています。',
    PASSWORD_RESET_RESEND_FAILED: '認証コードの再送信に失敗しました。',

    INIT_PASSWORD_TITLE: '初回パスワード設定',
    INIT_PASSWORD_NEW_PASSWORD: '新しいパスワード',
    INIT_PASSWORD_PASSWORD_PLACEHOLDER: '新しいパスワードを入力してください',
    INIT_PASSWORD_PASSWORD_HINT: '英数字を含む8-14文字で入力してください',
    INIT_PASSWORD_CONFIRM_PASSWORD: 'パスワード確認',
    INIT_PASSWORD_INPUT_SUBTITLE: '新しいパスワードを設定してください',
    INIT_PASSWORD_PROCESSING: '設定中...',
    INIT_PASSWORD_SUBMIT: 'パスワードを設定',
    INIT_PASSWORD_SUCCESS_MESSAGE: 'パスワードが正常に設定されました。',
    INIT_PASSWORD_FAILED_MESSAGE: 'パスワードの設定に失敗しました。',
    INIT_PASSWORD_ALREADY_ACTIVE_MESSAGE:
      'このアカウントが既に有効になりました。設定したパスワードでログインしてください。',
    INIT_PASSWORD_UNKNOWN_ERROR: 'エラーが発生しました。管理者へお問い合わせください。',
    INIT_PASSWORD_PASSWORD_ERROR: 'パスワードの形式が正しくありません。',
    INIT_PASSWORD_CONFIRM_ERROR: 'パスワードが一致しません。',
    INIT_PASSWORD_TOKEN_EXPIRED: 'トークンの有効期限が切れています。',
    INIT_PASSWORD_INVALID_ACCESS: '無効なアクセスです。',
    INIT_PASSWORD_NO_TOKEN: 'トークンが見つかりません。',
    INIT_PASSWORD_INVALID_TOKEN: '無効なトークンです。',
    INIT_PASSWORD_INVALID_TOKEN_MESSAGE: '無効なトークンです。',
  },

  en: {
    $vuetify: {
      ...enLocale,
      dataIterator: {
        rowsPerPageText: 'Number of items displayed:',
        pageText: '{0}-{1} of {2}',
      },
    },

    // Common translations
    COMMON_BACK: 'Back',
    COMMON_BACK_LIST: 'Back to List',
    COMMON_MORE: 'Show More',
    COMMON_JAPAN: 'Japan',
    COMMON_DUBAI: 'Dubai',
    COMMON_HONGKONG: 'Hong Kong',
    COMMON_SEND: 'Send',
    COMMON_AGREE: 'Agree',
    COMMON_ERROR: 'Error occurred.',
    COMMON_CONFIRM: 'Confirm',
    COMMON_INPUT_ERROR: 'Input Error',
    COMMON_DATE_FORMAT: 'yyyy/MM/dd',
    COMMON_REMOVE: 'Remove',
    COMMON_UPDATE_AUCTION: 'Update',
    COMMON_DAY: 'd',
    COMMON_HOUR: 'h',
    COMMON_MINUTE: 'm',
    COMMON_SECOND: 's',
    COMMON_MITAIOU: 'Pending',
    COMMON_LOADING: 'Loading',
    COMMON_OK_BUTTON: 'OK',
    ASCENDING_AUCTION: 'Ascending Auction',
    SEALED_AUCTION: 'Sealed Auction',

    // Site info
    SITE_TITLE: 'AUCTION │ クラウドECオークション',
    COPYRIGHT: '© 2004 GMO MAKESHOP Co. Ltd. All Rights Reserved.',

    // Top page app bar
    TOP_APP_BAR_SELECT_CATEGORY: 'Select Category',
    TOP_APP_BAR_REGISTER: 'New Member Registration',
    TOP_APP_BAR_LOGIN: 'Sign In',
    TOP_APP_BAR_LOGOUT: 'Sign Out',
    TOP_SEARCH_BY_CATEGORY: 'Search by Category',
    TOP_SEARCH_ALL_CATEGORIES: 'Search All Categories',
    TOP_SELECT_CATEGORY: 'Select Category',
    TOP_UPDATE_INFO: 'Update Info',
    TOP_RECOMMEND_PRODUCT: 'Recommended Products',
    TOP_NEW_PRODUCTS: 'New Products',

    // Top page about section
    TOP_ABOUT_TITLE: 'Auction site package is born',
    TOP_ABOUT_SUBTITLE: 'Cloud EC Auction',
    TOP_ABOUT_DESCRIPTION_1:
      'A package has been created to meet the demand for "easy hosting of in-house auctions".',
    TOP_ABOUT_DESCRIPTION_2:
      'This service packages the basic functions necessary for auctions and can be introduced at low cost. Site construction is hassle-free and can be introduced in a relatively short period of time.',
    TOP_ABOUT_DESCRIPTION_3: 'We also support customization to realize BtoB auction sites.',
    TOP_ABOUT_DESCRIPTION_4:
      'We provide member registration screening functions and business partner management functions, so you can hold auctions for business partners and limited members.',

    // Top page signup section
    TOP_SIGNUP_SUBTITLE: 'Registration is always free',
    TOP_SIGNUP_TITLE: 'Become a member and get the products you want at a great price!',
    TOP_SIGNUP_FREE_SHIPPING_TITLE: 'Free shipping benefits',
    TOP_SIGNUP_FREE_SHIPPING_DESC:
      'Members can use the scheduled delivery service free of charge for eligible products, excluding same-day delivery. For details, please see ',
    TOP_SIGNUP_FREE_SHIPPING_LINK: 'about scheduled delivery service',
    TOP_SIGNUP_FREE_SHIPPING_SUFFIX: '.',
    TOP_SIGNUP_COUPON_TITLE: 'Coupon distribution',
    TOP_SIGNUP_COUPON_DESC:
      'We regularly distribute discount coupons. To get coupons, you need to ',
    TOP_SIGNUP_COUPON_LOGIN_LINK: 'login',
    TOP_SIGNUP_COUPON_SUFFIX: '.',
    TOP_SIGNUP_MEMBER_MORE_BUTTON: 'Learn more about membership',
    TOP_SIGNUP_REGISTER_BUTTON: 'New member registration',

    // Header navigation
    HEADER_NAV_PRODUCT_CATEGORY: 'Category',
    HEADER_NAV_ALL_CATEGORIES: 'All Categories',
    HEADER_NAV_CATEGORY_1: 'Category 1',
    HEADER_NAV_CATEGORY_2: 'Category 2',
    HEADER_NAV_CATEGORY_3: 'Category 3',
    HEADER_NAV_SEARCH_PLACEHOLDER: 'Search by keyword',
    HEADER_NAV_SITE_ABOUT: 'About Site',
    HEADER_NAV_FIRST_TIME_VISITORS: 'First Time Visitors',
    HEADER_NAV_SHOPPING_GUIDE: 'Shopping Guide',
    HEADER_NAV_FAQ: 'FAQ',
    HEADER_NAV_CONTACT_US: 'Contact Us',
    HEADER_NAV_MEMBER_SERVICES: 'Member Services',
    HEADER_NAV_LOGIN: 'Sign In',
    HEADER_NAV_FAVORITES: 'Favorites',
    HEADER_NAV_BIDDING: 'Bidding',
    HEADER_NAV_SUCCESSFUL_BID_HISTORY: 'History',
    HEADER_NAV_REGISTER: 'Register',
    HEADER_NAV_LOGOUT: 'Sign Out',
    HEADER_NAV_NEW_MEMBER_REGISTER: 'New Member Registration',
    HEADER_NAV_MEMBER_MENU: 'Member Menu',
    HEADER_NAV_MY_PAGE: 'My Page',
    HEADER_NAV_GUIDANCE: 'Guidance',
    HEADER_NAV_COMPANY_INFO: 'Company Information',
    HEADER_NAV_SERVICE: 'Service',
    HEADER_NAV_MEMBERSHIP: 'Membership Services',
    HEADER_NAV_TOKUSHO: 'Specified Commercial Transaction Law',
    HEADER_NAV_TERMS_OF_SERVICE: 'Terms of Service',
    HEADER_NAV_SPECIFIED_COMMERCIAL_TRANSACTION_LAW: 'Commercial Transaction Act',
    HEADER_NAV_PRIVACY_POLICY: 'Privacy Policy',

    // Footer navigation
    FOOTER_NAV_PRODUCT_PURCHASE: 'Product Purchase',
    FOOTER_NAV_BAGS: 'Bags',
    FOOTER_NAV_OUTERWEAR: 'Outerwear',
    FOOTER_NAV_ACCESSORIES: 'Accessories',
    FOOTER_NAV_ABOUT_MEMBERSHIP: 'About Membership',
    FOOTER_NAV_LOGIN: 'Login',
    FOOTER_NAV_NEW_MEMBER_REGISTRATION: 'New Member Registration',
    FOOTER_NAV_MY_PAGE: 'My Page',
    FOOTER_NAV_FIRST_TIME_VISITORS: 'First Time Visitors',
    FOOTER_NAV_USAGE_GUIDE: 'Usage Guide',
    FOOTER_NAV_FAQ: 'FAQ',
    FOOTER_NAV_MEMBER_SERVICES: 'About Member Services',
    FOOTER_NAV_GUIDANCE: 'Guidance',
    FOOTER_NAV_NEWS: 'News',
    FOOTER_NAV_TERMS_OF_SERVICE: 'Terms of Service',
    FOOTER_NAV_COMPANY_OVERVIEW: 'Company Overview',
    FOOTER_NAV_CAMPAIGN_EARLY_BIRD: 'Campaign Early Bird Products',
    FOOTER_NAV_COUPON_PRODUCTS: 'Coupon Applicable Products',
    FOOTER_NAV_OFFICE_SUPPLIES: 'Office & Stationery',
    FOOTER_NAV_COMPUTERS: 'Computers',
    FOOTER_NAV_AV_ELECTRONICS: 'AV Equipment & Electronics',
    FOOTER_NAV_NEW_MEMBER_DISCOUNT: 'New Member Limited Discount',
    FOOTER_NAV_STORE_FIXTURES: 'Store Fixtures & Equipment',
    FOOTER_NAV_CONSTRUCTION_MATERIALS: 'Construction Materials & Waste',
    FOOTER_NAV_COMPANY_GUIDE: 'Company Guide',
    FOOTER_NAV_PRIVACY_POLICY: 'Privacy Policy',
    FOOTER_NAV_COMMERCIAL_TRANSACTION_LAW: 'Commercial Transaction Law',

    // Routes
    ROUTE_TOP: 'TOP',

    // Product detail
    DETAIL_TITLE: 'Product Details',
    DETAIL_DESCRIPTION: 'Product Description',
    DETAIL_CURRENCY: 'Yen',
    DETAIL_QUANTITY: 'Quantity',
    DETAIL_LOWEST_BID_QUANTITY: 'Minimum bid quantity',
    DETAIL_LOWEST_BID_PRICE: 'Minimum Bid Unit Price',
    DETAIL_BID_COUNT: 'Number of bids',
    DETAIL_BID_QUANTITY: 'Bid Quantity',
    DETAIL_BID_UNIT_PRICE: 'Bid Unit Price',
    NYUSATSU_KAKAKU: 'Bid Price',
    DETAIL_BID_TOTAL_PRICE: 'Total Bid Amount',
    DETAIL_BID_BUTTON: 'Place Bid',
    DETAIL_CONTACT_BUTTON: 'Inquiries about this product',
    DETAIL_ABOUT_RANK: 'Product Grade Details',
    DETAIL_CHAT: 'Chat with Seller',
    DETAIL_VIEW_COMMENTS: 'View Comments',
    DETAIL_CANCEL_BID: 'Cancel Bid',
    DETAIL_BID_HISTORY: 'Bid History',
    DETAIL_LAST_BID_TIME: 'Last Bid Time',
    DETAIL_LAST_BID_AMOUNT: 'Last Bid Amount',
    DETAIL_HIGHEST_BID_AMOUNT: 'Highest Bid Amount',
    DETAIL_BACK_TO_LIST: 'Back to List',

    // Product detail info
    DETAIL_INFO_MAKER: 'Manufacturer',
    DETAIL_INFO_PRODUCT_NAME: 'Product Name',
    DETAIL_INFO_SIM: 'SIM',
    DETAIL_INFO_CAPACITY: 'Capacity',
    DETAIL_INFO_COLOR: 'Color',
    DETAIL_INFO_RANK: 'Grade',
    DETAIL_INFO_QUANTITY: 'Quantity',
    DETAIL_INFO_NOTE1: 'Note 1',
    DETAIL_INFO_NOTE2: 'Note 2',
    DETAIL_INFO_LOWEST_BID_PRICE: 'Minimum Bid Price',
    DETAIL_INFO_LOWEST_BID_QUANTITY: 'Minimum Bid Quantity',
    DETAIL_INFO_FAVORITE: 'Favorite',
    DETAIL_INFO_START_PRICE: 'Starting Price',
    DETAIL_INFO_CURRENT_PRICE: 'Current Price',
    DETAIL_INFO_TAX_INCLUDED_PRICE: 'Price including tax',
    DETAIL_DEAL_BID_PRICE: 'Buy Now Price',
    DETAIL_BID_PERIOD: 'Bid Period',

    // Product detail bid modal
    ERROR_BID_MODAL_LOGIN_REQUIRED_MESSAGE:
      'Please login to place bids. Bidding is available for members only.',

    // Product detail error messages
    ERROR_PRICE_EMPTY: 'Please enter a bid price.',
    ERROR_PRICE_INVALID_FORMAT: 'Please enter numbers only for the bid price.',
    ERROR_PRICE_MAX_LENGTH:
      'Please enter the integer part within {0} digits and the decimal part within {1} digits.',
    ERROR_LOWEST_UNIT_PRICE_INVALID:
      'Please enter a value equal to or greater than the minimum bid unit price.',
    ERROR_NEW_PRICE_LOWER_THAN_CURRENT_PRICE_ERR: 'You cannot bid at or below the current price.',
    ERROR_QUANTITY_EMPTY: 'Please enter a bid quantity.',
    ERROR_QUANTITY_INVALID_FORMAT: 'Please enter numbers only for the bid quantity.',
    ERROR_QUANTITY_MAX_LENGTH: 'Please enter the bid quantity with {0} digits or less.',
    ERROR_LOWEST_BID_QUANTITY_INVALID:
      'Please enter a value equal to or greater than the minimum bid quantity.',
    ERROR_BID_QUANTITY_EXCEEDS_MAX: 'The bid quantity exceeds the available quantity.',

    // Bid Modal
    BID_MODAL_LOGIN_REQUIRED: 'Login is required.',
    BID_MODAL_BID_CANCELLED_SUCCESS: 'Bid has been cancelled.',
    BID_MODAL_CONFIRM_MESSAGE: 'Please click the bid button if everything is correct.',
    BID_MODAL_CANCEL_CONFIRMATION: 'Do you want to cancel the bid?',
    BID_MODAL_CONFIRM_BID: 'Confirm Bid',
    BID_VALIDATION_ERROR: 'Bid validation error occurred.',
    BID_ERROR_OCCURRED: 'An error occurred during bid processing.',
    BID_SUCCESS_MESSAGE: 'Bids have been accepted.',

    // Filter box
    FILTER_BOX_TITLE: 'Advanced Search',
    FILTER_BOX_CATEGORY: 'Category',
    SEARCH_RESULT: 'Search Results',
    PRODUCT_LIST: 'Product List',

    // Search functionality
    FILTER_BOX_SEARCH_ACTION: 'Search',
    FILTER_BOX_CLEAR_CONDITIONS_ACTION: 'Clear search conditions',
    FILTER_BOX_KEYWORD_FIELD: 'Keyword',
    FILTER_BOX_PRODUCT_SEARCH: 'Product Search',
    FILTER_BOX_SEARCH_PLACEHOLDER: 'Search by product name or keyword',
    FILTER_BOX_COUNT_UNIT: 'items',

    // Search results page
    SEARCH_RESULTS_SORT_RECOMMENDED: 'Recommended',
    SEARCH_RESULTS_SORT_NEWEST: 'Newest',
    SEARCH_RESULTS_SORT_TIME_REMAINING_ASC: 'Time Remaining (Ascending)',
    SEARCH_RESULTS_SORT_TIME_REMAINING_DESC: 'Time Remaining (Descending)',
    SEARCH_RESULTS_SORT_PRICE_ASC: 'Price (Low to High)',
    SEARCH_RESULTS_SORT_PRICE_DESC: 'Price (High to Low)',
    SEARCH_RESULTS_SORT_BID_COUNT_DESC: 'Most Bids',
    SEARCH_RESULTS_SORT_BID_COUNT_ASC: 'Least Bids',
    SEARCH_RESULTS_ON_SALE_ONLY: 'On Sale Only',
    SEARCH_RESULTS_DOWNLOAD_CSV_PRODUCT_INFO: 'Download Product Information (CSV)',
    SEARCH_RESULTS_DOWNLOAD_CSV_PRODUCT_HANDOVER: 'Download Product Handover (CSV)',
    SEARCH_RESULTS_DISPLAY_COUNT: 'Items per page',
    SEARCH_RESULTS_20_ITEMS: '20 items',
    SEARCH_RESULTS_50_ITEMS: '50 items',
    SEARCH_RESULTS_100_ITEMS: '100 items',
    SEARCH_RESULTS_PAGINATION_OF: 'of',
    SEARCH_RESULTS_PAGINATION_DISPLAYING: 'items displayed',

    MYPAGE_AUCTION_COUNT: 'auctions',

    // Product list
    LIST_CURRENT: 'Current Price',
    LIST_LOWEST_BID_PRICE: 'Lowest Bid Price',
    LIST_END_DATE_PLAN: 'End Date Plan',

    // Favorite
    FAVORITE_TITLE: 'Favorite',
    FAVORITE_EMPTY: 'Your favorites list is currently empty.',
    FAVORITE_CLEAR_PRICE_INPUT: 'Clear Input',
    FAVORITE_SUB_TOTAL_BID_PRICE_HEADER: 'Bidding<br>Subtotal',
    FAVORITE_SUB_TOTAL_BID_PRICE: 'Bidding Subtotal',
    FAVORITE_LOGIN_REQUIRED_FAVORITE:
      'You can put the item in your favorites list after signing in.',
    FAVORITE_BID_BUTTON: 'Place a bid',
    FAVORITE_RE_BID_BUTTON: 'Rebid',
    FAVORITE_BID_QUANTITY: 'Bid Quantity',
    FAVORITE_DELETE_FAVORITE1: 'Remove from',
    FAVORITE_DELETE_FAVORITE2: 'Favorites',

    // Bid history
    BID_HISTORY_END_DATE: 'End Date',
    BID_HISTORY_BID_SUCCESS_UNIT_PRICE: 'Winning Price',
    BID_HISTORY_BID_SUCCESS_PRICE: 'Winning Price',
    BID_HISTORY_BID_SUCCESS_QUANTITY: 'Winning Qty',
    BID_HISTORY_BID_TOTAL_PRICE: 'Total Amount',
    BID_HISTORY_INVOICE_DOWNLOAD: 'Download Invoice',
    BID_HISTORY_PAYMENT_STATUS_PAID: 'Paid',
    BID_HISTORY_PAYMENT_STATUS_PENDING: 'Processing',
    BID_HISTORY_PAYMENT_STATUS_UNPAID: 'Unpaid',
    BID_HISTORY_CURRENT_PRICE: 'Current Price',
    BID_HISTORY_ENDED: 'Ended',

    // Chat
    INQUIRY_CHAT: 'Inquiry Chat',
    CHAT_BACK_ITEM: 'Back to Product',
    CHAT_CHAT_ROOM: 'Chat Room',
    CHAT_SHOP: 'Shop',
    CHAT_HIDDEN_DESCRIPTION:
      'This post has been hidden because it was found to violate our policy.',
    CHAT_INPUT_PLACEHOLDER: 'Enter your message',
    CHAT_SEND_BUTTON: 'Post Message',
    CHAT_CLEAR_BUTTON: 'Clear',
    CHAT_ATTENTION_1:
      '※ Please do not enter any personal information such as your name or email address.',
    CHAT_ATTENTION_2: '※ Your message will be visible to other users.',
    CHAT_ATTENTION_3:
      '※ Please review your message before sending. Once posted, it cannot be edited or deleted.',
    ERROR_CHAT_LOGIN_REQUIRED_MESSAGE:
      'Please login to make an inquiry. Inquiries are available for members only.',

    // MY Page Member Edit
    MYPAGE_MEMBER_EDIT_LICENSE_COPY: 'A copy of your company’s Secondhand Dealer License',
    MYPAGE_MEMBER_EDIT_LICENSE_NOTES:
      'Our Export Control Compliance Notes with your company’s authorized signature',
    MYPAGE_MEMBER_EDIT_LICENSE_NOTES1:
      '※ Please send the original signed and sealed document by mail.',
    MYPAGE_MEMBER_EDIT_BUSINESS_CARD_COPY: 'A copy of the representative’s business card',
    MYPAGE_MEMBER_EDIT_DOWNLOAD: 'Download the required application documents',
    MYPAGE_MEMBER_EDIT_CONFIRM_BUTTON: 'Confirm Entered Information',
    MYPAGE_MEMBER_EDIT_WITHDRAW_BUTTON: 'Withdraw Membership',
    MYPAGE_MEMBER_EDIT_REQUIRED_LABEL: '※ Required',
    MYPAGE_MEMBER_EDIT_UPLOAD_LABEL: 'Upload',
    MYPAGE_MEMBER_EDIT_COMPLETE_MESSAGE: 'Member information has been updated.',
    MYPAGE_MEMBER_EDIT_CONFIRM: 'Check Your Input',

    // Auth
    AUTH_LOGOUT_MESSAGE: 'Would you like to sign out?',
    AUTH_LOGOUT: 'Sign out',
    AUTH_CLOSE: 'Close',
    AUTH_CANCEL: 'Cancel',

    // Cognito standardized error messages
    AUTH_COGNITO_USERNAME_EXISTS_EXCEPTION: 'This email address is already registered.',
    AUTH_COGNITO_INVALID_PASSWORD_EXCEPTION: 'Password does not meet policy requirements.',
    AUTH_COGNITO_INVALID_PARAMETER_EXCEPTION: 'Invalid input.',
    AUTH_COGNITO_CODE_MISMATCH_EXCEPTION: 'The verification code is incorrect.',
    AUTH_COGNITO_LIMIT_EXCEEDED_EXCEPTION: 'Request limit exceeded. Please try again later.',
    AUTH_COGNITO_EXPIRED_CODE_EXCEPTION: 'The verification code has expired.',
    AUTH_COGNITO_NOT_AUTHORIZED_EXCEPTION: 'Authentication failed.',
    AUTH_COGNITO_CODE_DELIVERY_FAILURE_EXCEPTION: 'Failed to send verification code.',
    AUTH_COGNITO_USER_NOT_CONFIRMED_EXCEPTION: 'This account has not been verified.',
    AUTH_COGNITO_PASSWORD_RESET_REQUIRED_EXCEPTION: 'Password reset is required.',
    AUTH_COGNITO_USER_NOT_FOUND_EXCEPTION: 'This email address is not registered.',
    AUTH_COGNITO_COGNITO_UNKNOWN_ERROR: 'An unknown error occurred.',

    // Login
    LOGIN_TITLE: 'Sign in',
    LOGIN_SUBTITLE: 'login',
    LOGIN_ID: 'Login ID',
    LOGIN_EMAIL: 'Email Address',
    LOGIN_EMAIL_CONFIRM: 'Email Address (Confirmation)',
    LOGIN_PASSWORD: 'Password',
    LOGIN_REQUIRED: '※Required',
    LOGIN_PLACEHOLDER_ID_PASSWORD: '8-14 alphanumeric characters',
    LOGIN_SAVE_ID_PASSWORD: 'Save ID and Password',
    LOGIN_SAVE_LOGIN_INFO: 'Save email address and password',
    LOGIN_FORGET_PASSWORD: 'Forgot Password?',
    LOGIN_FORGET_PASSWORD_LINK: 'Forgot Your Password?',
    LOGIN_RULE: 'Terms of Service',
    LOGIN_RULE_TITLE: 'Bidding Participation Terms',
    LOGIN_AGREE_RULE: 'I agree to the Terms of Service',
    LOGIN_AGREE: 'Agree',
    LOGIN_ENTRY_INFO1: 'New Member Registration (Free)',
    LOGIN_ENTRY_INFO2: 'Registration is required to bid on items.',
    LOGIN_NEW_MEMBER_REGISTER: 'New Member Registration',
    LOGIN_PRICE_VIEW_REQUIRES_REGISTRATION: '※Registration is required to view product prices.',
    LOGIN_CONFIRM_BUTTON: 'Sign in',
    LOGIN_PROCESSING: 'Processing...',
    LOGIN_PASSWORD_HINT: '8-16 alphanumeric characters',
    LOGIN_VALIDATION_NOT_ENTERED: 'This field is required',
    LOGIN_VALIDATION_PASSWORD_LENGTH: 'Password must be 8-14 characters',
    LOGIN_VALIDATION_AGREE_TERMS: 'Please agree to the terms of service',
    LOGIN_ERROR_INVALID_CREDENTIALS: 'Invalid login ID or password.',
    LOGIN_ERROR_INVALID_OTP_CODE: 'Invalid verification code. Please enter the correct code.',
    LOGIN_ERROR_OTP_SESSION_EXPIRED:
      'The session may have expired. Please restart the login process.',
    LOGIN_ERROR_LOGIN_FAILED: 'Login failed. Please check your information.',
    LOGIN_ERROR_NEW_PASSWORD_REQUIRED: 'Password change is required for first-time login.',
    LOGIN_ERROR_UNKNOWN: 'An unknown error occurred.',
    LOGIN_ERROR_USER_NOT_CONFIRMED: 'This account has not been verified.',
    LOGIN_ERROR_USER_ALREADY_AUTHENTICATED: 'You are already logged in.',
    LOGIN_ERROR_LIMIT_EXCEEDED: 'Login attempt limit exceeded. Please try again later.',
    LOGIN_ERROR_INVALID_PASSWORD: 'Password does not meet policy requirements.',
    LOGIN_ERROR_TOO_MANY_REQUESTS: 'Too many requests. Please try again later.',
    LOGIN_ERROR_LOGIN_HISTORY_FAILED: 'Failed to log login history.',
    LOGIN_ERROR_EMAIL_VERIFICATION_REQUIRED: 'Email verification is required.',
    LOGIN_ERROR_EMAIL_CODE_REQUIRED: 'Email verification code is required.',
    LOGIN_ERROR_UNSUPPORTED_STEP: 'Unsupported authentication step:',
    LOGIN_ERROR_MEMBER_STATUS_FAILED: 'Failed to retrieve member status.',
    LOGIN_ERROR_LOGIN_FAILED_BY_STATUS:
      'You cannot log in because your membership has been suspended or withdrawn.',

    // OTP (One-Time Password) Page
    OTP_TITLE: 'Enter Verification Code',
    OTP_SUBTITLE: 'Enter your verification code',
    OTP_EMAIL_SENT_MESSAGE: 'A verification code has been sent to your registered email address.',
    OTP_ENTER_CODE_INSTRUCTION: 'Please enter the 6-digit verification code.',
    OTP_CODE_GROUP_LABEL: '6-digit verification code',
    OTP_DIGIT_SUFFIX: ' digit',
    OTP_AUTHENTICATING: 'Authenticating...',
    OTP_AUTHENTICATE: 'Authenticate',
    OTP_RESEND_CODE: 'Resend verification code',
    OTP_AUTHENTICATION_FAILED: 'Authentication failed.',
    OTP_INVALID_CODE: 'The verification code is incorrect.',
    OTP_RESEND_IN_DEVELOPMENT:
      'The resend verification code feature is currently under development.',

    // Password Reminder
    LOGIN_REMINDER_TITLE: 'Forgot Your Password?',
    LOGIN_REMINDER_SUBTITLE: 'Reminder',
    LOGIN_REMINDER_MESSAGE1:
      'If you forgot your password, please enter your registered email address.',
    LOGIN_REMINDER_MESSAGE2:
      'Press the "Send" button and your password reset code will be sent to your registered email address.',
    LOGIN_REMINDER_USER_ID: 'ID',
    LOGIN_REMINDER_USER_ID_PLACEHOLDER: 'Alphanumeric characters',
    LOGIN_REMINDER_SEND_BUTTON: 'Send',
    LOGIN_REMINDER_COMPLETE_MESSAGE:
      'Password has been reset successfully. Please log in with your new password.',
    LOGIN_REMINDER_BACK_TO_LOGIN: 'Back to Login',
    LOGIN_REMINDER_EMAIL_ERROR: 'Email address is invalid',
    LOGIN_REMINDER_CONFIRM_EMAIL_ERROR: 'Email addresses do not match',
    MYPAGE_EDIT_PROFILE: 'Edit profile',
    MYPAGE_EDIT_CONFIRM: 'Edit Confirmation',
    MYPAGE_CARD: 'Card information',

    // Payment Form
    PAYMENT_CURRENT_INFO: 'Current Payment Information',
    PAYMENT_COMPLETED: 'Payment has been completed.',
    PAYMENT_PENDING: 'Payment is pending. Please check again later.',
    PAYMENT_FAILED: 'Payment failed. Please try with a different card.',
    PAYMENT_NOT_PROCESSED: 'Payment has not been processed yet. Please check again later.',
    CARD_LIST: 'Card List',
    CARD_ADD: 'Add Card',
    CARD_SET_DEFAULT: 'Set as Default',
    CARD_DELETE: 'Delete',
    PAYMENT_BUTTON: 'Payment',
    PAYMENT_COMPLETE: 'Payment Complete',
    PAYMENT_COMPLETE_MESSAGE: 'Payment has been completed.',
    PAYMENT_FAIL: 'Payment Failed',
    PAYMENT_FAIL_MESSAGE: 'Payment has failed.',
    CARD_DEFAULT_CHANGE_COMPLETE: 'Default Card Change Complete',
    CARD_DEFAULT_CHANGE_MESSAGE: 'Default card has been changed.',
    CARD_DELETE_COMPLETE: 'Card Deletion Complete',
    CARD_DELETE_MESSAGE: 'Card has been deleted.',
    CARD_EDIT: 'Edit',
    CARD_NO_DATA: 'No cards are currently registered.',
    CARD_NO: 'Card Number',
    CARD_DEFAULT: 'Default Card',

    CARD_INPUT_REQUIRED: 'Required',
    CARD_HOLDER_NAME: 'Cardholder Name',
    CARD_HOLDER_NAME_PLACEHOLDER: 'e.g. TARO YAMADA',
    CARD_NUMBER: 'Card Number',
    CARD_NUMBER_PLACEHOLDER: 'e.g. 1234-5678-XXXX-XXXX',
    CARD_EXPIRATION: 'Expiration Date',
    CARD_EXPIRATION_MONTH: 'Month',
    CARD_EXPIRATION_MONTH_PLACEHOLDER: 'e.g. 05',
    CARD_EXPIRATION_YEAR: 'Year',
    CARD_EXPIRATION_YEAR_PLACEHOLDER: 'e.g. 25',
    CARD_SECURITY_CODE: 'Security Code',
    CARD_SECURITY_CODE_PLACEHOLDER: 'e.g. 123',
    CARD_DEFAULT_SETTING: 'Usage Settings',
    CARD_DEFAULT_SETTING_TEXT: 'Set as default for regular use',

    CARD_INPUT_HALF_WIDTH: 'Half-width alphanumeric',
    CARD_COMPLETE_DIALOG_CLOSE: 'Close',

    CARD_CONFIRM_DIALOG_DEFAULT_MESSAGE:
      'Are you sure you want to set the following card as default?',
    CARD_CONFIRM_DIALOG_DELETE_MESSAGE: 'Are you sure you want to delete the following card?',
    CARD_CONFIRM_DIALOG_CLOSE: 'Close',
    CARD_CONFIRM_DIALOG_UPDATE: 'Update',
    CARD_CONFIRM_DIALOG_DELETE: 'Delete',

    CARD_EDIT_DIALOG_TITLE: 'Edit Credit Card Information',
    CARD_EDIT_DIALOG_CARD_NO: 'Card Number',
    CARD_EDIT_DIALOG_EXPIRE: 'Expiration Date',
    CARD_EDIT_DIALOG_SECURITY_CODE: 'Security Code',
    CARD_EDIT_DIALOG_USE_SETTING: 'Usage Settings',
    CARD_EDIT_DIALOG_USE_SETTING_TEXT: 'Set as default for regular use',
    CARD_EDIT_DIALOG_CONFIRM: 'Confirm',

    PAYMENT_COMPLETE_DIALOG_TITLE: 'Payment Process Complete',
    PAYMENT_COMPLETE_DIALOG_MESSAGE: 'Your payment process has been completed.',
    PAYMENT_COMPLETE_DIALOG_NOTE:
      'You will receive a confirmation email from the shop within 3 business days. Please check your email for details.',
    PAYMENT_COMPLETE_DIALOG_BACK_TO_LIST: 'Back to Bid History',

    PAYMENT_CONFIRM_DIALOG_TITLE: 'Payment Method',
    PAYMENT_CONFIRM_DIALOG_MESSAGE:
      'Do you want to proceed with the payment process with this content?',
    PAYMENT_CONFIRM_DIALOG_PROCESSING: 'Payment processing is in progress. Please wait a moment.',
    PAYMENT_CONFIRM_DIALOG_CREDIT_CARD: 'Credit Card',
    PAYMENT_CONFIRM_DIALOG_CREDIT_CARD_1TIME: '（One-time payment）',
    PAYMENT_CONFIRM_DIALOG_PRIVACY_POLICY_1: '※Payment is subject to',
    PAYMENT_CONFIRM_DIALOG_PRIVACY_POLICY_2: 'Privacy Policy',
    PAYMENT_CONFIRM_DIALOG_PRIVACY_POLICY_3: 'and',
    PAYMENT_CONFIRM_DIALOG_PRIVACY_POLICY_4: 'Terms of Use',
    PAYMENT_CONFIRM_DIALOG_PRIVACY_POLICY_5: 'by placing your order.',
    PAYMENT_CONFIRM_DIALOG_PRIVACY_POLICY_6:
      '※Credit cards with names different from the customer cannot be used.',
    PAYMENT_CONFIRM_DIALOG_BACK_TO_CARD_LIST: 'Back to Credit Card Selection',
    PAYMENT_CONFIRM_DIALOG_CONFIRM_PAYMENT: 'Confirm Payment',

    PAYMENT_ITEM_INFO_TITLE: 'Auction Item',
    PAYMENT_ITEM_INFO_PRODUCT_NAME: 'Product Name',
    PAYMENT_ITEM_INFO_BID_SUCCESS_PRICE: 'Winning Bid Price',
    PAYMENT_ITEM_INFO_TAX_INCLUDED_PRICE: 'Tax Included Price',
    PAYMENT_ITEM_INFO_TOTAL_PRICE: 'Total Price',
    PAYMENT_ITEM_INFO_FREE_SHIPPING: 'Free Shipping',

    PAYMENT_FORM_TITLE: 'Payment Method',
    PAYMENT_FORM_SELECT_CARD: 'Use Registered Card',
    PAYMENT_FORM_SELECT_CARD_LABEL: 'Select Card',
    PAYMENT_FORM_NO_CARDS: 'No credit cards are currently registered.',
    PAYMENT_FORM_SELECT_CARD_CARD_NO: 'Card Number',
    PAYMENT_FORM_SELECT_CARD_EXPIRE: 'Expiration Date',
    PAYMENT_FORM_ADD_NEW_CARD: 'Use New Credit Card',
    PAYMENT_FORM_ADD_NEW_CARD_MESSAGE:
      '※The card used this time will be registered as a payment method in My Page.',
    PAYMENT_FORM_NEXT_BUTTON: 'Confirm Payment Details',

    PAYMENT_RESULT_TITLE: 'Payment Results',
    PAYMENT_RESULT_PROCESSING: 'Payment processing is in progress. Please wait a moment...',
    PAYMENT_RESULT_COMPLETED: 'Your payment process has been completed.',
    PAYMENT_RESULT_PENDING: 'Payment is pending. Please check again later.',
    PAYMENT_RESULT_FAILED: 'Payment failed. Please try a different card.',
    PAYMENT_RESULT_NOT_PROCESSED: 'Payment has not been processed yet. Please check again later.',
    PAYMENT_RESULT_COMPLETED_NOTE:
      'You will receive a confirmation email from the shop within 3 business days. Please check your email for details.',
    PAYMENT_RESULT_BACK_TO_LIST: 'Back to Bid History',
    PAYMENT_RESULT_ACCESS_ID_NOT_FOUND: 'Transaction ID not found.',
    PAYMENT_RESULT_3DS_ERROR: '3DS2.0 authentication failed after payment execution.',
    PAYMENT_RESULT_UNKNOWN_ERROR: 'Failed to retrieve payment status.',

    // Existing uppercase keys (keep as-is)
    COMMON_BID_LABEL: 'Bid',
    BID_COUNT: '',
    CLASSIFICATION_ASCENDING: 'Ascending Auction',
    CLASSIFICATION_SEALED: 'Sealed Auction',
    ASCENDING: 'Ascending',
    SEALED: 'Sealed',
    BID_STATUS_INPROGRESS: 'In Progress',
    BID_STATUS_CANCEL: 'Suspension of exhibits',
    BID_STATUS_NOT_START_YET: 'Waiting to Start',
    BID_STATUS_ENDED: 'End',
    BID_STATUS_PRE_AUCTION: 'Pre-Auction',
    BID_STATUS_EXTENDING: 'Auction Extended',
    DURING_BIDDING_PERIOD: 'During the bidding period',
    HIGHEST_BIDDER: 'Highest Bidder',
    HIGHEST_BID_AMOUNT: 'Highest Bid Amount',
    BID_STATUS: 'Status',
    REMAINING_TIME: 'Remaining time',
    YOU_ARE_TOP: 'Top bidder',
    RESERVE_PRICE_NOT_MET: 'The minimum bid price has not been reached.',
    RESERVE_PRICE_MET: 'The minimum bid price has been reached.',
    YOU_ARE_NOT_TOP: 'You are not the top bidder',
    MORE_LITTLE: 'Just A little more',
    SECOND_BIDDER: '2nd place bidder',
    END_DATE_TIME: 'End date and time',
    START_DATE_TIME: 'Start date and time',
    RECORDED_BID_PRICE: 'Your Price:',

    // Date picker
    DATE_PICKER_DATE_FORMAT: 'yyyyMMdd',
    DATE_PICKER_YEAR: 'Year',
    DATE_PICKER_MONTH: 'Month',
    DATE_PICKER_DAY: 'Day',

    // Custom translation(jp key)
    UNIT_BID_AVAILABLE: 'You can bid in units',
    SCHEDULED_END: 'Ends at',
    SHARE_THIS_PRODUCT: 'Share this product',

    // 会員登録画面
    MEMBER_REGISTER_TITLE: 'Member Registration',
    MEMBER_REGISTER_SUBTITLE: 'Member Registration',
    REGISTER_FORM_REQUIRED: '※Required',
    REGISTER_FORM_CONFIRM_BUTTON: 'Confirm Input',
    REGISTER_FORM_REGISTER_FINISH: 'Registration completed.',
    REGISTER_SUBTITLE_CONFIRM: 'Confirmation',
    PASSWORD_COMPARE_ERROR: 'Passwords do not match.',

    // Member confirmation screen
    MEMBER_CONFIRM_TITLE: 'Member Confirmation',
    MEMBER_CONFIRM_LOADING: 'Verifying...',
    MEMBER_CONFIRM_SUCCESS_TITLE: 'Verified!',
    MEMBER_CONFIRM_SUCCESS_MESSAGE: 'You have successfully verified account.',
    MEMBER_CONFIRM_ERROR_TITLE: 'Error',
    MEMBER_CONFIRM_ERROR_MESSAGE: 'An error occurred during verification.',
    MEMBER_CONFIRM_TOKEN_NOT_FOUND: 'Verification token not found.',
    MEMBER_CONFIRM_BUTTON: 'Ok',

    // Member application required documents
    MEMBER_APPLICATION_REQUIRED_DOCUMENTS: 'Required Documents for Member Application',
    MEMBER_APPLICATION_REVIEW_INFO_1:
      'We conduct a review process for member registration applications.',
    MEMBER_APPLICATION_REVIEW_INFO_2: 'You can download the required documents from below.',
    MEMBER_APPLICATION_ANTIQUE_DEALER_LICENSE: 'Copy of Antique Dealer License',
    MEMBER_APPLICATION_ANTIQUE_DEALER_TOOLTIP:
      'Please copy so that the antique dealer license number is visible.',
    MEMBER_APPLICATION_EXPORT_MANAGEMENT_NOTICE:
      "Export Management Notice with Your Company's Signature",
    MEMBER_APPLICATION_EXPORT_MANAGEMENT_TOOLTIP: 'Signature and seal are required.',
    MEMBER_APPLICATION_REPRESENTATIVE_BUSINESS_CARD: "Copy of Representative's Business Card",
    MEMBER_APPLICATION_REPRESENTATIVE_CARD_TOOLTIP:
      'Please prepare only the front side of the business card.',
    MEMBER_APPLICATION_SIGNED_DOCUMENT_NOTE:
      '※Please mail the original signed and sealed document.',
    MEMBER_APPLICATION_DOWNLOAD_BUTTON: 'Download Required Application Documents',
    MEMBER_APPLICATION_UPLOAD_BUTTON: 'Upload',

    // Notice
    NOTICE_BACK_TO_LIST: 'Back to Notice List',
    NOTICE_EMPTY: 'No notifications',

    // Forgot Password
    FORGOT_PASSWORD_CODE_SENT: 'Password reset verification code has been sent.',
    FORGOT_PASSWORD_USER_NOT_FOUND: 'This email address is not registered.',
    FORGOT_PASSWORD_LIMIT_EXCEEDED: 'Request limit exceeded. Please try again later.',
    FORGOT_PASSWORD_INVALID_PARAMETER: 'Invalid parameter.',
    FORGOT_PASSWORD_UNKNOWN_ERROR: 'An unknown error occurred.',
    FORGOT_PASSWORD_RESET_SUCCESS: 'Password has been successfully reset.',
    FORGOT_PASSWORD_INVALID_CODE: 'The verification code is incorrect.',
    FORGOT_PASSWORD_EXPIRED_CODE: 'The verification code has expired.',
    FORGOT_PASSWORD_INVALID_PASSWORD: 'Password does not meet policy requirements.',

    // Password Reset UI
    LOGIN_REMINDER_REQUEST_ERROR: 'Failed to request password reset.',
    LOGIN_REMINDER_OTP_SENT_MESSAGE: 'Password reset verification code has been sent via email.',
    LOGIN_REMINDER_EMAIL_SENT_TO: 'Sent to',
    LOGIN_REMINDER_OTP_INSTRUCTION: 'Please enter the 6-digit verification code from the email.',
    LOGIN_REMINDER_ENTER_OTP: 'Enter Verification Code',
    LOGIN_REMINDER_SENDING: 'Sending...',
    LOGIN_REMINDER_EMAIL_PLACEHOLDER: 'Enter your email address',
    LOGIN_REMINDER_EMAIL_CONFIRM_PLACEHOLDER: 'Re-enter your email address',

    PASSWORD_RESET_TITLE: 'Password Reset',
    PASSWORD_RESET_SUBTITLE: 'Password',
    PASSWORD_RESET_OTP_CODE: 'Verification Code',
    PASSWORD_RESET_OTP_PLACEHOLDER: 'Enter 6-digit verification code',
    PASSWORD_RESET_NEW_PASSWORD: 'Password',
    PASSWORD_RESET_PASSWORD_PLACEHOLDER: '8-14 alphanumeric characters',
    PASSWORD_RESET_CONFIRM_PASSWORD: 'Password(Confirmation)',
    PASSWORD_RESET_SUBMIT: 'Reset Password',
    PASSWORD_RESET_PROCESSING: 'Processing...',
    PASSWORD_RESET_SUCCESS_MESSAGE:
      'Your password has been successfully reset. Please log in with your new password.',
    PASSWORD_RESET_FAILED_MESSAGE: 'Failed to reset password.',
    PASSWORD_RESET_UNKNOWN_ERROR: 'An unknown error occurred.',
    PASSWORD_RESET_EMAIL_MISSING: 'Email address is not set.',
    PASSWORD_RESET_PASSWORD_ERROR: 'Password format is incorrect.',
    PASSWORD_RESET_CONFIRM_ERROR: 'Passwords do not match.',

    PASSWORD_RESET_OTP_TITLE: 'Enter Verification Code',
    PASSWORD_RESET_OTP_SUBTITLE: 'Please enter the verification code',
    PASSWORD_RESET_OTP_EMAIL_SENT_MESSAGE:
      'Password reset verification code has been sent via email.',
    PASSWORD_RESET_OTP_ENTER_CODE_INSTRUCTION:
      'Please enter the 6-digit verification code from the email.',
    PASSWORD_RESET_OTP_VERIFYING: 'Verifying...',
    PASSWORD_RESET_OTP_VERIFY: 'Verify',
    PASSWORD_RESET_OTP_RESEND_CODE: 'Resend Verification Code',
    PASSWORD_RESET_OTP_INVALID_CODE: 'The verification code is incorrect.',
    PASSWORD_RESET_OTP_VALID: 'The verification code has been successfully verified.',
    PASSWORD_RESET_OTP_EXPIRED_CODE: 'The verification code has expired.',
    PASSWORD_RESET_RESEND_FAILED: 'Failed to resend verification code.',

    INIT_PASSWORD_TITLE: 'Set Initial Password',
    INIT_PASSWORD_NEW_PASSWORD: 'New Password',
    INIT_PASSWORD_PASSWORD_PLACEHOLDER: 'Enter your new password',
    INIT_PASSWORD_PASSWORD_HINT: 'Must be 8-14 characters with letters and numbers',
    INIT_PASSWORD_CONFIRM_PASSWORD: 'Confirm Password',
    INIT_PASSWORD_INPUT_SUBTITLE: 'Please set your new password',
    INIT_PASSWORD_PROCESSING: 'Setting...',
    INIT_PASSWORD_SUBMIT: 'Set Password',
    INIT_PASSWORD_SUCCESS_MESSAGE: 'Your password has been set successfully.',
    INIT_PASSWORD_FAILED_MESSAGE: 'Failed to set password.',
    INIT_PASSWORD_ALREADY_ACTIVE_MESSAGE:
      'Your account is already active. Please log in with your existing password.',
    INIT_PASSWORD_UNKNOWN_ERROR: 'An unknown error occurred.',
    INIT_PASSWORD_PASSWORD_ERROR: 'Password format is incorrect.',
    INIT_PASSWORD_CONFIRM_ERROR: 'Passwords do not match.',
    INIT_PASSWORD_TOKEN_EXPIRED: 'The token has expired.',
    INIT_PASSWORD_INVALID_ACCESS: 'Invalid access.',
    INIT_PASSWORD_NO_TOKEN: 'Token not found.',
    INIT_PASSWORD_INVALID_TOKEN: 'Invalid token.',
    INIT_PASSWORD_INVALID_TOKEN_MESSAGE: 'Invalid token.',
  },
}

// Type for the t() function
export type TranslateFunction = (key: keyof TranslationKeys) => string

// Utility type to ensure both languages have the same keys
export type EnsureKeysMatch<T extends Record<string, TranslationKeys>> = {
  [K in keyof T]: TranslationKeys
} & T

// Type guard to ensure translate object has matching keys
export const validateTranslation = <T extends Record<string, TranslationKeys>>(
  translations: EnsureKeysMatch<T>
): EnsureKeysMatch<T> => {
  return translations
}
