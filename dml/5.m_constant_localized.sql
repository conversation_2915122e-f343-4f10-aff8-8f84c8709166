INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '管理者権限(管理者)'),
    'ja',
    '10',
    '管理者',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '管理者権限(管理者)'),
    'en',
    '10',
    'admin',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '管理者権限(運用担当者)'),
    'ja',
    '20',
    '運用担当者',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '管理者権限(運用担当者)'),
    'en',
    '20',
    'operator',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '管理者権限(一般)'),
    'ja',
    '30',
    '一般',
    '',
    '',
    ''
);
-- INSERT INTO m_constant_localized(
--     tenant_no,
--     constant_no,
--     language_code,
--     value1,
--     value2,
--     value3,
--     value4,
--     value5
-- ) VALUES(
--     1,
--     (SELECT constant_no FROM m_constant WHERE value_name = '管理者権限(一般)'),
--     'en',
--     '30',
--     'normal',
--     '',
--     '',
--     ''
-- );
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = 'オークション方式(オークション)'),
    'ja',
    '1',
    'せりあげ',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = 'オークション方式(オークション)'),
    'en',
    '1',
    'Auction',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = 'オークション方式(テンダー)'),
    'ja',
    '2',
    '封印',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = 'オークション方式(テンダー)'),
    'en',
    '2',
    'Sealed Bid',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (
        SELECT constant_no
        FROM m_constant WHERE value_name = '落札価格決定区分(ファーストプライス)'
    ),
    'ja',
    '1',
    'ファーストプライス',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (
        SELECT constant_no
        FROM m_constant WHERE value_name = '落札価格決定区分(ファーストプライス)'
    ),
    'en',
    '1',
    'first price',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (
        SELECT constant_no
        FROM m_constant WHERE value_name = '落札価格決定区分(セカンドプライス)'
    ),
    'ja',
    '2',
    'セカンドプライス',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (
        SELECT constant_no
        FROM m_constant WHERE value_name = '落札価格決定区分(セカンドプライス)'
    ),
    'en',
    '2',
    'second price',
    '',
    '',
    ''
);

-- 国一覧 --
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Japan)'),
    'ja',
    'JP',
    '日本',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Japan)'),
    'en',
    'JP',
    'Japan',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Iceland)'),
    'ja',
    'IS',
    'アイスランド',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Iceland)'),
    'en',
    'IS',
    'Iceland',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Ireland)'),
    'ja',
    'IE',
    'アイルランド',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Ireland)'),
    'en',
    'IE',
    'Ireland',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Azerbaijan)'),
    'ja',
    'AZ',
    'アゼルバイジャン',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Azerbaijan)'),
    'en',
    'AZ',
    'Azerbaijan',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Afghanistan)'),
    'ja',
    'AF',
    'アフガニスタン',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Afghanistan)'),
    'en',
    'AF',
    'Afghanistan',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Abkhazia, Republic of)'),
    'ja',
    'ABR',
    'アブハジア',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Abkhazia, Republic of)'),
    'en',
    'ABR',
    'Abkhazia, Republic of',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(United States)'),
    'ja',
    'US',
    'アメリカ',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(United States)'),
    'en',
    'US',
    'United States',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(United Arab Emirates)'),
    'ja',
    'AE',
    'アラブ首長国連邦',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(United Arab Emirates)'),
    'en',
    'AE',
    'United Arab Emirates',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Algeria)'),
    'ja',
    'DZ',
    'アルジェリア',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Algeria)'),
    'en',
    'DZ',
    'Algeria',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Argentina)'),
    'ja',
    'AR',
    'アルゼンチン',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Argentina)'),
    'en',
    'AR',
    'Argentina',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Albania)'),
    'ja',
    'AL',
    'アルバニア',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Albania)'),
    'en',
    'AL',
    'Albania',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Armenia)'),
    'ja',
    'AM',
    'アルメニア',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Armenia)'),
    'en',
    'AM',
    'Armenia',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Angola)'),
    'ja',
    'AO',
    'アンゴラ',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Angola)'),
    'en',
    'AO',
    'Angola',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Antigua and Barbuda)'),
    'ja',
    'AG',
    'アンティグア・バーブーダ',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Antigua and Barbuda)'),
    'en',
    'AG',
    'Antigua and Barbuda',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Andorra)'),
    'ja',
    'AD',
    'アンドラ',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Andorra)'),
    'en',
    'AD',
    'Andorra',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Yemen)'),
    'ja',
    'YE',
    'イエメン',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Yemen)'),
    'en',
    'YE',
    'Yemen',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(United Kingdom)'),
    'ja',
    'GB',
    'イギリス',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(United Kingdom)'),
    'en',
    'GB',
    'United Kingdom',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Israel)'),
    'ja',
    'IL',
    'イスラエル',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Israel)'),
    'en',
    'IL',
    'Israel',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Italy)'),
    'ja',
    'IT',
    'イタリア',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Italy)'),
    'en',
    'IT',
    'Italy',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Iraq)'),
    'ja',
    'IQ',
    'イラク',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Iraq)'),
    'en',
    'IQ',
    'Iraq',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Iran, Islamic Republic of)'),
    'ja',
    'IR',
    'イラン',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Iran, Islamic Republic of)'),
    'en',
    'IR',
    'Iran, Islamic Republic of',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(India)'),
    'ja',
    'IN',
    'インド',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(India)'),
    'en',
    'IN',
    'India',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Indonesia)'),
    'ja',
    'ID',
    'インドネシア',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Indonesia)'),
    'en',
    'ID',
    'Indonesia',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Uganda)'),
    'ja',
    'UG',
    'ウガンダ',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Uganda)'),
    'en',
    'UG',
    'Uganda',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Ukraine)'),
    'ja',
    'UA',
    'ウクライナ',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Ukraine)'),
    'en',
    'UA',
    'Ukraine',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Uzbekistan)'),
    'ja',
    'UZ',
    'ウズベキスタン',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Uzbekistan)'),
    'en',
    'UZ',
    'Uzbekistan',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Uruguay)'),
    'ja',
    'UY',
    'ウルグアイ',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Uruguay)'),
    'en',
    'UY',
    'Uruguay',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Ecuador)'),
    'ja',
    'EC',
    'エクアドル',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Ecuador)'),
    'en',
    'EC',
    'Ecuador',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Egypt)'),
    'ja',
    'EG',
    'エジプト',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Egypt)'),
    'en',
    'EG',
    'Egypt',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Estonia)'),
    'ja',
    'EE',
    'エストニア',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Estonia)'),
    'en',
    'EE',
    'Estonia',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Eswatini（Swaziland）)'),
    'ja',
    'SZ',
    'エスワティニ（スワジランド）',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Eswatini（Swaziland）)'),
    'en',
    'SZ',
    'Eswatini（Swaziland）',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Ethiopia)'),
    'ja',
    'ET',
    'エチオピア',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Ethiopia)'),
    'en',
    'ET',
    'Ethiopia',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Eritrea)'),
    'ja',
    'ER',
    'エリトリア',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Eritrea)'),
    'en',
    'ER',
    'Eritrea',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(El Salvador)'),
    'ja',
    'SV',
    'エルサルバドル',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(El Salvador)'),
    'en',
    'SV',
    'El Salvador',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Pridnestrovian Moldavian Republic)'),
    'ja',
    'PMR',
    '沿ドニエストル',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Pridnestrovian Moldavian Republic)'),
    'en',
    'PMR',
    'Pridnestrovian Moldavian Republic',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Australia)'),
    'ja',
    'AU',
    'オーストラリア',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Australia)'),
    'en',
    'AU',
    'Australia',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Austria)'),
    'ja',
    'AT',
    'オーストリア',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Austria)'),
    'en',
    'AT',
    'Austria',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Oman)'),
    'ja',
    'OM',
    'オマーン',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Oman)'),
    'en',
    'OM',
    'Oman',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Netherlands)'),
    'ja',
    'NL',
    'オランダ',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Netherlands)'),
    'en',
    'NL',
    'Netherlands',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Ghana)'),
    'ja',
    'GH',
    'ガーナ',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Ghana)'),
    'en',
    'GH',
    'Ghana',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Cape Verde)'),
    'ja',
    'CV',
    'カーボベルデ',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Cape Verde)'),
    'en',
    'CV',
    'Cape Verde',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Guyana)'),
    'ja',
    'GY',
    'ガイアナ',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Guyana)'),
    'en',
    'GY',
    'Guyana',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Kazakhstan)'),
    'ja',
    'KZ',
    'カザフスタン',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Kazakhstan)'),
    'en',
    'KZ',
    'Kazakhstan',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Qatar)'),
    'ja',
    'QA',
    'カタール',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Qatar)'),
    'en',
    'QA',
    'Qatar',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Canada)'),
    'ja',
    'CA',
    'カナダ',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Canada)'),
    'en',
    'CA',
    'Canada',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Gabon)'),
    'ja',
    'GA',
    'ガボン',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Gabon)'),
    'en',
    'GA',
    'Gabon',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Cameroon)'),
    'ja',
    'CM',
    'カメルーン',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Cameroon)'),
    'en',
    'CM',
    'Cameroon',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Korea, Republic of)'),
    'ja',
    'KR',
    '韓国',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Korea, Republic of)'),
    'en',
    'KR',
    'Korea, Republic of',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Gambia)'),
    'ja',
    'GM',
    'ガンビア',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Gambia)'),
    'en',
    'GM',
    'Gambia',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Cambodia)'),
    'ja',
    'KH',
    'カンボジア',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Cambodia)'),
    'en',
    'KH',
    'Cambodia',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Northern Cyprus)'),
    'ja',
    'NCY',
    '北キプロス',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Northern Cyprus)'),
    'en',
    'NCY',
    'Northern Cyprus',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Korea, Democratic People''s Republic of)'),
    'ja',
    'KP',
    '北朝鮮',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Korea, Democratic People''s Republic of)'),
    'en',
    'KP',
    'Korea, Democratic People''s Republic of',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Guinea)'),
    'ja',
    'GN',
    'ギニア',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Guinea)'),
    'en',
    'GN',
    'Guinea',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Guinea-Bissau)'),
    'ja',
    'GW',
    'ギニアビサウ',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Guinea-Bissau)'),
    'en',
    'GW',
    'Guinea-Bissau',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Cyprus)'),
    'ja',
    'CY',
    'キプロス',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Cyprus)'),
    'en',
    'CY',
    'Cyprus',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Cuba)'),
    'ja',
    'CU',
    'キューバ',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Cuba)'),
    'en',
    'CU',
    'Cuba',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Greece)'),
    'ja',
    'GR',
    'ギリシャ',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Greece)'),
    'en',
    'GR',
    'Greece',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Kiribati)'),
    'ja',
    'KI',
    'キリバス',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Kiribati)'),
    'en',
    'KI',
    'Kiribati',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Kyrgyzstan)'),
    'ja',
    'KG',
    'キルギス',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Kyrgyzstan)'),
    'en',
    'KG',
    'Kyrgyzstan',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Guatemala)'),
    'ja',
    'GT',
    'グアテマラ',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Guatemala)'),
    'en',
    'GT',
    'Guatemala',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Kuwait)'),
    'ja',
    'KW',
    'クウェート',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Kuwait)'),
    'en',
    'KW',
    'Kuwait',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Cook Islands)'),
    'ja',
    'CK',
    'クック諸島',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Cook Islands)'),
    'en',
    'CK',
    'Cook Islands',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Grenada)'),
    'ja',
    'GD',
    'グレナダ',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Grenada)'),
    'en',
    'GD',
    'Grenada',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Croatia)'),
    'ja',
    'HR',
    'クロアチア',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Croatia)'),
    'en',
    'HR',
    'Croatia',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Kenya)'),
    'ja',
    'KE',
    'ケニア',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Kenya)'),
    'en',
    'KE',
    'Kenya',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Côte d''Ivoire)'),
    'ja',
    'CI',
    'コートジボワール',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Côte d''Ivoire)'),
    'en',
    'CI',
    'Côte d''Ivoire',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Costa Rica)'),
    'ja',
    'CR',
    'コスタリカ',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Costa Rica)'),
    'en',
    'CR',
    'Costa Rica',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Kosovo)'),
    'ja',
    'KOS',
    'コソボ',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Kosovo)'),
    'en',
    'KOS',
    'Kosovo',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Comoros)'),
    'ja',
    'KM',
    'コモロ',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Comoros)'),
    'en',
    'KM',
    'Comoros',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Colombia)'),
    'ja',
    'CO',
    'コロンビア',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Colombia)'),
    'en',
    'CO',
    'Colombia',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Congo)'),
    'ja',
    'CG',
    'コンゴ共和国',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Congo)'),
    'en',
    'CG',
    'Congo',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Congo, the Democratic Republic of the)'),
    'ja',
    'CD',
    'コンゴ民主共和国',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Congo, the Democratic Republic of the)'),
    'en',
    'CD',
    'Congo, the Democratic Republic of the',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Saudi Arabia)'),
    'ja',
    'SA',
    'サウジアラビア',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Saudi Arabia)'),
    'en',
    'SA',
    'Saudi Arabia',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Samoa)'),
    'ja',
    'WS',
    'サモア',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Samoa)'),
    'en',
    'WS',
    'Samoa',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Sao Tome and Principe)'),
    'ja',
    'ST',
    'サントメ・プリンシペ',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Sao Tome and Principe)'),
    'en',
    'ST',
    'Sao Tome and Principe',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Zambia)'),
    'ja',
    'ZM',
    'ザンビア',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Zambia)'),
    'en',
    'ZM',
    'Zambia',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(San Marino)'),
    'ja',
    'SM',
    'サンマリノ',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(San Marino)'),
    'en',
    'SM',
    'San Marino',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Sierra Leone)'),
    'ja',
    'SL',
    'シエラレオネ',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Sierra Leone)'),
    'en',
    'SL',
    'Sierra Leone',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Djibouti)'),
    'ja',
    'DJ',
    'ジブチ',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Djibouti)'),
    'en',
    'DJ',
    'Djibouti',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Jamaica)'),
    'ja',
    'JM',
    'ジャマイカ',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Jamaica)'),
    'en',
    'JM',
    'Jamaica',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Georgia)'),
    'ja',
    'GE',
    'ジョージア',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Georgia)'),
    'en',
    'GE',
    'Georgia',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Syrian Arab Republic)'),
    'ja',
    'SY',
    'シリア',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Syrian Arab Republic)'),
    'en',
    'SY',
    'Syrian Arab Republic',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Singapore)'),
    'ja',
    'SG',
    'シンガポール',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Singapore)'),
    'en',
    'SG',
    'Singapore',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Zimbabwe)'),
    'ja',
    'ZW',
    'ジンバブエ',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Zimbabwe)'),
    'en',
    'ZW',
    'Zimbabwe',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Switzerland)'),
    'ja',
    'CH',
    'スイス',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Switzerland)'),
    'en',
    'CH',
    'Switzerland',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Sweden)'),
    'ja',
    'SE',
    'スウェーデン',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Sweden)'),
    'en',
    'SE',
    'Sweden',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Sudan)'),
    'ja',
    'SD',
    'スーダン',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Sudan)'),
    'en',
    'SD',
    'Sudan',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Spain)'),
    'ja',
    'ES',
    'スペイン',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Spain)'),
    'en',
    'ES',
    'Spain',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Suriname)'),
    'ja',
    'SR',
    'スリナム',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Suriname)'),
    'en',
    'SR',
    'Suriname',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Sri Lanka)'),
    'ja',
    'LK',
    'スリランカ',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Sri Lanka)'),
    'en',
    'LK',
    'Sri Lanka',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Slovakia)'),
    'ja',
    'SK',
    'スロバキア',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Slovakia)'),
    'en',
    'SK',
    'Slovakia',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Slovenia)'),
    'ja',
    'SI',
    'スロベニア',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Slovenia)'),
    'en',
    'SI',
    'Slovenia',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Seychelles)'),
    'ja',
    'SC',
    'セーシェル',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Seychelles)'),
    'en',
    'SC',
    'Seychelles',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Equatorial Guinea)'),
    'ja',
    'GQ',
    '赤道ギニア',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Equatorial Guinea)'),
    'en',
    'GQ',
    'Equatorial Guinea',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Senegal)'),
    'ja',
    'SN',
    'セネガル',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Senegal)'),
    'en',
    'SN',
    'Senegal',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Serbia)'),
    'ja',
    'RS',
    'セルビア',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Serbia)'),
    'en',
    'RS',
    'Serbia',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Saint Kitts and Nevis)'),
    'ja',
    'KN',
    'セントクリストファー・ネイビス',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Saint Kitts and Nevis)'),
    'en',
    'KN',
    'Saint Kitts and Nevis',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Saint Vincent and the Grenadines)'),
    'ja',
    'VC',
    'セントビンセントおよびグレナディーン諸島',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Saint Vincent and the Grenadines)'),
    'en',
    'VC',
    'Saint Vincent and the Grenadines',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Saint Lucia)'),
    'ja',
    'LC',
    'セントルシア',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Saint Lucia)'),
    'en',
    'LC',
    'Saint Lucia',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Somalia)'),
    'ja',
    'SO',
    'ソマリア',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Somalia)'),
    'en',
    'SO',
    'Somalia',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Somaliland, Republic of)'),
    'ja',
    'SOR',
    'ソマリランド',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Somaliland, Republic of)'),
    'en',
    'SOR',
    'Somaliland, Republic of',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Solomon Islands)'),
    'ja',
    'SB',
    'ソロモン諸島',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Solomon Islands)'),
    'en',
    'SB',
    'Solomon Islands',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Thailand)'),
    'ja',
    'TH',
    'タイ',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Thailand)'),
    'en',
    'TH',
    'Thailand',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Taiwan, Province of China)'),
    'ja',
    'TW',
    '台湾',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Taiwan, Province of China)'),
    'en',
    'TW',
    'Taiwan, Province of China',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Tajikistan)'),
    'ja',
    'TJ',
    'タジキスタン',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Tajikistan)'),
    'en',
    'TJ',
    'Tajikistan',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Tanzania, United Republic of)'),
    'ja',
    'TZ',
    'タンザニア',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Tanzania, United Republic of)'),
    'en',
    'TZ',
    'Tanzania, United Republic of',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Czechia)'),
    'ja',
    'CZ',
    'チェコ',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Czechia)'),
    'en',
    'CZ',
    'Czechia',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Chad)'),
    'ja',
    'TD',
    'チャド',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Chad)'),
    'en',
    'TD',
    'Chad',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Central African Republic)'),
    'ja',
    'CF',
    '中央アフリカ共和国',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Central African Republic)'),
    'en',
    'CF',
    'Central African Republic',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(China)'),
    'ja',
    'CN',
    '中国',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(China)'),
    'en',
    'CN',
    'China',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Tunisia)'),
    'ja',
    'TN',
    'チュニジア',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Tunisia)'),
    'en',
    'TN',
    'Tunisia',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Chile)'),
    'ja',
    'CL',
    'チリ',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Chile)'),
    'en',
    'CL',
    'Chile',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Tuvalu)'),
    'ja',
    'TV',
    'ツバル',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Tuvalu)'),
    'en',
    'TV',
    'Tuvalu',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Denmark)'),
    'ja',
    'DK',
    'デンマーク',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Denmark)'),
    'en',
    'DK',
    'Denmark',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Germany)'),
    'ja',
    'DE',
    'ドイツ',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Germany)'),
    'en',
    'DE',
    'Germany',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Togo)'),
    'ja',
    'TG',
    'トーゴ',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Togo)'),
    'en',
    'TG',
    'Togo',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Dominican Republic)'),
    'ja',
    'DO',
    'ドミニカ共和国',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Dominican Republic)'),
    'en',
    'DO',
    'Dominican Republic',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Dominica)'),
    'ja',
    'DM',
    'ドミニカ国',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Dominica)'),
    'en',
    'DM',
    'Dominica',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Trinidad and Tobago)'),
    'ja',
    'TT',
    'トリニダード・トバゴ',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Trinidad and Tobago)'),
    'en',
    'TT',
    'Trinidad and Tobago',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Turkmenistan)'),
    'ja',
    'TM',
    'トルクメニスタン',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Turkmenistan)'),
    'en',
    'TM',
    'Turkmenistan',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Turkey)'),
    'ja',
    'TR',
    'トルコ',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Turkey)'),
    'en',
    'TR',
    'Turkey',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Tonga)'),
    'ja',
    'TO',
    'トンガ',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Tonga)'),
    'en',
    'TO',
    'Tonga',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Nigeria)'),
    'ja',
    'NG',
    'ナイジェリア',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Nigeria)'),
    'en',
    'NG',
    'Nigeria',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Nauru)'),
    'ja',
    'NR',
    'ナウル',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Nauru)'),
    'en',
    'NR',
    'Nauru',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Nagorno Karabagh Republic)'),
    'ja',
    'NKR',
    'ナゴルノ・カラバフ',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Nagorno Karabagh Republic)'),
    'en',
    'NKR',
    'Nagorno Karabagh Republic',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Namibia)'),
    'ja',
    'NA',
    'ナミビア',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Namibia)'),
    'en',
    'NA',
    'Namibia',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Niue)'),
    'ja',
    'NU',
    'ニウエ',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Niue)'),
    'en',
    'NU',
    'Niue',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Nicaragua)'),
    'ja',
    'NI',
    'ニカラグア',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Nicaragua)'),
    'en',
    'NI',
    'Nicaragua',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Niger)'),
    'ja',
    'NE',
    'ニジェール',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Niger)'),
    'en',
    'NE',
    'Niger',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Western Sahara)'),
    'ja',
    'EH',
    '西サハラ',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Western Sahara)'),
    'en',
    'EH',
    'Western Sahara',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(New Zealand)'),
    'ja',
    'NZ',
    'ニュージーランド',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(New Zealand)'),
    'en',
    'NZ',
    'New Zealand',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Nepal)'),
    'ja',
    'NP',
    'ネパール',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Nepal)'),
    'en',
    'NP',
    'Nepal',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Norway)'),
    'ja',
    'NO',
    'ノルウェー',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Norway)'),
    'en',
    'NO',
    'Norway',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Bahrain)'),
    'ja',
    'BH',
    'バーレーン',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Bahrain)'),
    'en',
    'BH',
    'Bahrain',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Haiti)'),
    'ja',
    'HT',
    'ハイチ',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Haiti)'),
    'en',
    'HT',
    'Haiti',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Pakistan)'),
    'ja',
    'PK',
    'パキスタン',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Pakistan)'),
    'en',
    'PK',
    'Pakistan',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Holy See (Vatican City State))'),
    'ja',
    'VA',
    'バチカン',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Holy See (Vatican City State))'),
    'en',
    'VA',
    'Holy See (Vatican City State)',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Panama)'),
    'ja',
    'PA',
    'パナマ',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Panama)'),
    'en',
    'PA',
    'Panama',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Vanuatu)'),
    'ja',
    'VU',
    'バヌアツ',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Vanuatu)'),
    'en',
    'VU',
    'Vanuatu',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Bahamas)'),
    'ja',
    'BS',
    'バハマ',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Bahamas)'),
    'en',
    'BS',
    'Bahamas',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Papua New Guinea)'),
    'ja',
    'PG',
    'パプアニューギニア',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Papua New Guinea)'),
    'en',
    'PG',
    'Papua New Guinea',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Palau)'),
    'ja',
    'PW',
    'パラオ',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Palau)'),
    'en',
    'PW',
    'Palau',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Paraguay)'),
    'ja',
    'PY',
    'パラグアイ',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Paraguay)'),
    'en',
    'PY',
    'Paraguay',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Barbados)'),
    'ja',
    'BB',
    'バルバドス',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Barbados)'),
    'en',
    'BB',
    'Barbados',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Palestinian Territory, Occupied)'),
    'ja',
    'PS',
    'パレスチナ',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Palestinian Territory, Occupied)'),
    'en',
    'PS',
    'Palestinian Territory, Occupied',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Hungary)'),
    'ja',
    'HU',
    'ハンガリー',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Hungary)'),
    'en',
    'HU',
    'Hungary',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Bangladesh)'),
    'ja',
    'BD',
    'バングラデシュ',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Bangladesh)'),
    'en',
    'BD',
    'Bangladesh',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Timor-Leste)'),
    'ja',
    'TL',
    '東ティモール',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Timor-Leste)'),
    'en',
    'TL',
    'Timor-Leste',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Fiji)'),
    'ja',
    'FJ',
    'フィジー',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Fiji)'),
    'en',
    'FJ',
    'Fiji',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Philippines)'),
    'ja',
    'PH',
    'フィリピン',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Philippines)'),
    'en',
    'PH',
    'Philippines',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Finland)'),
    'ja',
    'FI',
    'フィンランド',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Finland)'),
    'en',
    'FI',
    'Finland',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Bhutan)'),
    'ja',
    'BT',
    'ブータン',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Bhutan)'),
    'en',
    'BT',
    'Bhutan',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Brazil)'),
    'ja',
    'BR',
    'ブラジル',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Brazil)'),
    'en',
    'BR',
    'Brazil',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(France)'),
    'ja',
    'FR',
    'フランス',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(France)'),
    'en',
    'FR',
    'France',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Bulgaria)'),
    'ja',
    'BG',
    'ブルガリア',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Bulgaria)'),
    'en',
    'BG',
    'Bulgaria',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Burkina Faso)'),
    'ja',
    'BF',
    'ブルキナファソ',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Burkina Faso)'),
    'en',
    'BF',
    'Burkina Faso',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Brunei Darussalam)'),
    'ja',
    'BN',
    'ブルネイ',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Brunei Darussalam)'),
    'en',
    'BN',
    'Brunei Darussalam',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Burundi)'),
    'ja',
    'BI',
    'ブルンジ',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Burundi)'),
    'en',
    'BI',
    'Burundi',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Viet Nam)'),
    'ja',
    'VN',
    'ベトナム',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Viet Nam)'),
    'en',
    'VN',
    'Viet Nam',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Benin)'),
    'ja',
    'BJ',
    'ベナン',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Benin)'),
    'en',
    'BJ',
    'Benin',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Venezuela, Bolivarian Republic of)'),
    'ja',
    'VE',
    'ベネズエラ',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Venezuela, Bolivarian Republic of)'),
    'en',
    'VE',
    'Venezuela, Bolivarian Republic of',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Belarus)'),
    'ja',
    'BY',
    'ベラルーシ',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Belarus)'),
    'en',
    'BY',
    'Belarus',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Belize)'),
    'ja',
    'BZ',
    'ベリーズ',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Belize)'),
    'en',
    'BZ',
    'Belize',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Peru)'),
    'ja',
    'PE',
    'ペルー',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Peru)'),
    'en',
    'PE',
    'Peru',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Belgium)'),
    'ja',
    'BE',
    'ベルギー',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Belgium)'),
    'en',
    'BE',
    'Belgium',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Poland)'),
    'ja',
    'PL',
    'ポーランド',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Poland)'),
    'en',
    'PL',
    'Poland',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Bosnia and Herzegovina)'),
    'ja',
    'BA',
    'ボスニア・ヘルツェゴビナ',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Bosnia and Herzegovina)'),
    'en',
    'BA',
    'Bosnia and Herzegovina',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Botswana)'),
    'ja',
    'BW',
    'ボツワナ',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Botswana)'),
    'en',
    'BW',
    'Botswana',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Bolivia, Plurinational State of)'),
    'ja',
    'BO',
    'ボリビア',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Bolivia, Plurinational State of)'),
    'en',
    'BO',
    'Bolivia, Plurinational State of',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Portugal)'),
    'ja',
    'PT',
    'ポルトガル',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Portugal)'),
    'en',
    'PT',
    'Portugal',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Honduras)'),
    'ja',
    'HN',
    'ホンジュラス',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Honduras)'),
    'en',
    'HN',
    'Honduras',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Macedonia, the former Yugoslav Republic of)'),
    'ja',
    'MK',
    'マケドニア旧ユーゴスラビア共和国',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Macedonia, the former Yugoslav Republic of)'),
    'en',
    'MK',
    'Macedonia, the former Yugoslav Republic of',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Marshall Islands)'),
    'ja',
    'MH',
    'マーシャル諸島',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Marshall Islands)'),
    'en',
    'MH',
    'Marshall Islands',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Madagascar)'),
    'ja',
    'MG',
    'マダガスカル',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Madagascar)'),
    'en',
    'MG',
    'Madagascar',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Malawi)'),
    'ja',
    'MW',
    'マラウイ',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Malawi)'),
    'en',
    'MW',
    'Malawi',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Mali)'),
    'ja',
    'ML',
    'マリ',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Mali)'),
    'en',
    'ML',
    'Mali',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Malta)'),
    'ja',
    'MT',
    'マルタ',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Malta)'),
    'en',
    'MT',
    'Malta',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Malaysia)'),
    'ja',
    'MY',
    'マレーシア',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Malaysia)'),
    'en',
    'MY',
    'Malaysia',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Micronesia, Federated States of)'),
    'ja',
    'FM',
    'ミクロネシア連邦',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Micronesia, Federated States of)'),
    'en',
    'FM',
    'Micronesia, Federated States of',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(South Africa)'),
    'ja',
    'ZA',
    '南アフリカ',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(South Africa)'),
    'en',
    'ZA',
    'South Africa',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(South Ossetia)'),
    'ja',
    'SOS',
    '南オセチア',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(South Ossetia)'),
    'en',
    'SOS',
    'South Ossetia',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(South Sudan)'),
    'ja',
    'SS',
    '南スーダン',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(South Sudan)'),
    'en',
    'SS',
    'South Sudan',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Myanmar)'),
    'ja',
    'MM',
    'ミャンマー',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Myanmar)'),
    'en',
    'MM',
    'Myanmar',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Mexico)'),
    'ja',
    'MX',
    'メキシコ',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Mexico)'),
    'en',
    'MX',
    'Mexico',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Mauritius)'),
    'ja',
    'MU',
    'モーリシャス',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Mauritius)'),
    'en',
    'MU',
    'Mauritius',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Mauritania)'),
    'ja',
    'MR',
    'モーリタニア',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Mauritania)'),
    'en',
    'MR',
    'Mauritania',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Mozambique)'),
    'ja',
    'MZ',
    'モザンビーク',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Mozambique)'),
    'en',
    'MZ',
    'Mozambique',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Monaco)'),
    'ja',
    'MC',
    'モナコ',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Monaco)'),
    'en',
    'MC',
    'Monaco',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Maldives)'),
    'ja',
    'MV',
    'モルディブ',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Maldives)'),
    'en',
    'MV',
    'Maldives',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Moldova, Republic of)'),
    'ja',
    'MD',
    'モルドバ',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Moldova, Republic of)'),
    'en',
    'MD',
    'Moldova, Republic of',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Morocco)'),
    'ja',
    'MA',
    'モロッコ',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Morocco)'),
    'en',
    'MA',
    'Morocco',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Mongolia)'),
    'ja',
    'MN',
    'モンゴル',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Mongolia)'),
    'en',
    'MN',
    'Mongolia',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Montenegro)'),
    'ja',
    'ME',
    'モンテネグロ',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Montenegro)'),
    'en',
    'ME',
    'Montenegro',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Jordan)'),
    'ja',
    'JO',
    'ヨルダン',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Jordan)'),
    'en',
    'JO',
    'Jordan',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Lao People''s Democratic Republic)'),
    'ja',
    'LA',
    'ラオス',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Lao People''s Democratic Republic)'),
    'en',
    'LA',
    'Lao People''s Democratic Republic',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Latvia)'),
    'ja',
    'LV',
    'ラトビア',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Latvia)'),
    'en',
    'LV',
    'Latvia',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Lithuania)'),
    'ja',
    'LT',
    'リトアニア',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Lithuania)'),
    'en',
    'LT',
    'Lithuania',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Libya)'),
    'ja',
    'LY',
    'リビア',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Libya)'),
    'en',
    'LY',
    'Libya',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Liechtenstein)'),
    'ja',
    'LI',
    'リヒテンシュタイン',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Liechtenstein)'),
    'en',
    'LI',
    'Liechtenstein',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Liberia)'),
    'ja',
    'LR',
    'リベリア',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Liberia)'),
    'en',
    'LR',
    'Liberia',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Romania)'),
    'ja',
    'RO',
    'ルーマニア',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Romania)'),
    'en',
    'RO',
    'Romania',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Luxembourg)'),
    'ja',
    'LU',
    'ルクセンブルク',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Luxembourg)'),
    'en',
    'LU',
    'Luxembourg',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Rwanda)'),
    'ja',
    'RW',
    'ルワンダ',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Rwanda)'),
    'en',
    'RW',
    'Rwanda',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Lesotho)'),
    'ja',
    'LS',
    'レソト',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Lesotho)'),
    'en',
    'LS',
    'Lesotho',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Lebanon)'),
    'ja',
    'LB',
    'レバノン',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Lebanon)'),
    'en',
    'LB',
    'Lebanon',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Russian Federation)'),
    'ja',
    'RU',
    'ロシア',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '国(Russian Federation)'),
    'en',
    'RU',
    'Russian Federation',
    '',
    '',
    ''
);

-- Default pitch --
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = 'デフォルトピッチ幅'),
    'ja',
    '2', -- Tender
    '1',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = 'デフォルトピッチ幅'),
    'ja',
    '1', -- Auction
    '1',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = 'デフォルトピッチ幅'),
    'en',
    '2',
    '1',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = 'デフォルトピッチ幅'),
    'en',
    '1',
    '1',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '表示先'),
    'ja',
    '1',
    '全員へ公開',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '表示先'),
    'ja',
    '2',
    'ログイン者のみ',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '入札受付完了のお知らせ'),
    'ja',
    'Saas Auction　入札受付のお知らせ',
    '<EMAIL>', -- FROM
    '',
    'Saas Auctionへのご参加ありがとうございます。以下の商品に入札を受け付けました。

{0}

{1}',
    '商品名,入札価格,入札数'
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '入札受付完了のお知らせ'),
    'en',
    'Saas Auction Bid Submission Confirmation',
    '<EMAIL>', -- FROM
    '',
    'Thank you for participating in Saas Auction.

We have received your bid for the following item(s):

{0}

{1}',
    'Product name,Bid price,Bid quantity'
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (
        SELECT constant_no
        FROM m_constant WHERE value_name = 'メールテンプレート(会員自身による会員情報修正 管理者向け)'
    ),
    'ja',
    'Saas Auction　会員情報が変更されました',
    '<EMAIL>', -- FROM
    '<EMAIL>', -- BCC
    '{0}様の会員情報が変更されました。

【会員情報】
会員ID：{1}
会社名：{2}
電話番号：{3}
メールアドレス：{4}',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (
        SELECT constant_no
        FROM m_constant WHERE value_name = 'メールテンプレート(会員自身による会員情報修正 管理者向け)'
    ),
    'en',
    'Saas Auction: Membership Information Has Been Updated',
    '<EMAIL>', -- FROM
    '<EMAIL>', -- BCC
    'The membership information for {0} has been updated.

[Membership Information]
Membership ID: {1}
Company Name: {2}
Phone Number: {3}
Email Address: {4}',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (
        SELECT constant_no
        FROM m_constant WHERE value_name = 'メールテンプレート(会員自身による会員情報修正: 会員向け)'
    ),
    'ja',
    'Saas Auction　会員情報変更のお知らせ',
    '<EMAIL>', -- FROM
    '', -- BCC
    'Saas Auctionをご利用いただきありがとうございます。
会員情報の変更を受け付けましたのでお知らせいたします。

{0}',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (
        SELECT constant_no
        FROM m_constant WHERE value_name = 'メールテンプレート(会員自身による会員情報修正: 会員向け)'
    ),
    'en',
    'Saas Auction: Notification of Membership Information Update',
    '<EMAIL>', -- FROM
    '', -- BCC
    'Thank you for using Saas Auction.
We are pleased to inform you that your membership information update has been received.

{0}',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = 'メールテンプレート(共通フッター)'),
    'ja',
    'テンプレート1',
    '',
    '',
    'Saas Auction事務局　株式会社GMO
https://saas-1-auction.stage.auction.custom-ec.com

＊このメールは送信専用アドレスから送信しています。Saas Auction事務局へのご連絡は
https://saas-1-auction.stage.auction.custom-ec.com/contact
からお願いいたします。',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = 'メールテンプレート(共通フッター)'),
    'en',
    'template1',
    '',
    '',
    'Saas Auction Secretariat, GMO Co., Ltd.
https://saas-1-auction.stage.auction.custom-ec.com

This email is sent from a no-reply address. For inquiries to the Saas Auction Secretariat, please visit
https://saas-1-auction.stage.auction.custom-ec.com/contact',
''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '入札会終了通知メール'),
    'ja',
    'Saas Auction　オークション結果確定のお知らせ',
    '<EMAIL>', -- FROM
    '',
    '{0}　様

Saas Auctionへのご参加ありがとうございます。
以下のオークションが終了しましたのでお知らせいたします。

オークション名：{1}

{2}

落札商品に関しては落札数量、落札金額を後日、弊社担当者よりご連絡差し上げます。
その他、ご不明なことがございましたら弊社担当者までご連絡をお願いいたします。

{3}',
    '【落札商品】,【不落商品】,商品名,落札価格,落札価格(税込)' -- 不落告知はテンダーのみ
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '入札会終了通知メール'),
    'en',
    'Saas Auction Notification of Auction Results Finalization',
    '<EMAIL>', -- FROM
    '',
    'Dear {0},

Thank you for participating in Saas Auction.
We are pleased to inform you that the following auction has ended.

Auction Name: {1}

{2}

Regarding the auctioned items, our representative will contact you later with details on the winning quantity and price.
If you have any further questions, please feel free to reach out to our representative.

{3}',
    '【Successful bid】,【Not Successful bid】,Product name,Winning bid price,Winning bid price(Tax included)'
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (
        SELECT constant_no
        FROM m_constant WHERE value_name = 'メールテンプレート(パスワード忘れ: 会員向け)'
    ),
    'ja',
    'Saas Auction　パスワードリセットのお知らせ',
    '<EMAIL>', -- FROM
    '',
    '新しいパスワードは以下となります。

{0}

{1}',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (
        SELECT constant_no
        FROM m_constant WHERE value_name = 'メールテンプレート(パスワード忘れ: 会員向け)'
    ),
    'en',
    'Saas Auction Your password',
    '<EMAIL>', -- FROM
    '',
    'We have reset your password. Please find out your password as below.

{0}

{1}',
    ''
);

-- EMAIL_FORGOT_PASSWORD_OTP Japanese
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (
        SELECT constant_no
        FROM m_constant WHERE value_name = 'メールテンプレート(パスワードリセットOTP: 会員向け)'
    ),
    'ja',
    'Saas Auction　パスワードリセット認証コード',
    '<EMAIL>', -- FROM
    '',
    'パスワードリセットのご依頼を承りました。

以下の認証コードを入力してパスワードをリセットしてください：

認証コード: {{otp_code}}

もしこのリクエストにお心当たりがない場合は、このメールを無視してください。

Saas Auction事務局',
    ''
);

-- EMAIL_FORGOT_PASSWORD_OTP English
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (
        SELECT constant_no
        FROM m_constant WHERE value_name = 'メールテンプレート(パスワードリセットOTP: 会員向け)'
    ),
    'en',
    'Saas Auction Password Reset Verification Code',
    '<EMAIL>', -- FROM
    '',
    'We have received a request to reset your password for your Saas Auction account.

Please enter the following verification code to reset your password:

Verification Code: {{otp_code}}

If you did not request this password reset, please ignore this email.

Best regards,
Saas Auction Team',
    ''
);

INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (
        SELECT constant_no
        FROM m_constant WHERE value_name = 'メールテンプレート(問い合わせ: 管理者向け)'
    ),
    'ja',
    'Saas Auction　お問い合わせがありました', -- Title
    '<EMAIL>', -- FROM
    '<EMAIL>', -- BCC
    '{0}様からお問い合わせがありました。

【会員情報】
会員ID：{1}
会社名：{2}
電話番号：{3}
メールアドレス：{4}
{5}
【お問い合わせ内容】
{6}',
'【商品情報】,商品名' -- value 5
);

INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (
        SELECT constant_no
        FROM m_constant WHERE value_name = 'メールテンプレート(問い合わせ: 管理者向け)'
    ),
    'en',
    'Saas Auction: An Inquiry Has Been Received', -- Title
    '<EMAIL>', -- FROM
    '<EMAIL>', -- BCC
    'An inquiry has been received from {0}.

[Membership Information]
Member ID: {1}
Company Name: {2}
Tel: {3}
Email Address: {4}
{5}
[Inquiry Details]
{6}',
'[Product information],Product name' -- value 5
);

INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (
        SELECT constant_no
        FROM m_constant WHERE value_name = 'メールテンプレート(問い合わせ: 会員向け)'
    ),
    'ja',
    'Saas Auction　お問い合わせを受付のお知らせ', -- Title
    '<EMAIL>', -- FROM
    '', -- BCC
    'Saas Auctionにお問い合わせいただきありがとうございます。

下記内容にて受け付けいたしましたので、後日担当よりご連絡差し上げます。
今しばらくお待ちくださいますよう、よろしくお願い申し上げます。
{0}
【お問い合わせ内容】
{1}

{2}',  -- 0: [商品情報], 1: [お問い合わせ内容]  {2}: Footer
'【商品情報】,商品名' -- value5
);

INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (
        SELECT constant_no
        FROM m_constant WHERE value_name = 'メールテンプレート(問い合わせ: 会員向け)'
    ),
    'en',
    'Saas Auction We have received your inquiry', -- Title
    '<EMAIL>',-- FROM
    '', -- BCC
    'Thank you for contacting Saas Auction.

We have received your inquiry as detailed below, and our representative will get in touch with you shortly.
We kindly ask for your patience in the meantime.
{0}
[Inquiry Details]
{1}

{2}',  -- 0: [商品情報], 1: [お問い合わせ内容]  {2}: Footer
'[Product information],Product name' -- value5
);

INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (
        SELECT constant_no
        FROM m_constant WHERE value_name = 'メールテンプレート(会員承認: 会員向け)'
    ),
    'ja',
    'Saas Auction　会員登録完了のお知らせ',
    '<EMAIL>', -- FROM
    '',
    'Saas Auctionをご利用いただきありがとうございます。
会員登録が完了しましたのでお知らせいたします。

ログイン時のメールアドレスとパスワードは申請時のものをご利用ください。

{0}',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (
        SELECT constant_no
        FROM m_constant WHERE value_name = 'メールテンプレート(会員承認: 会員向け)'
    ),
    'en',
    'Saas Auction Thank you very much for membership registration',
    '<EMAIL>', -- FROM
    '',
    'Thank you for using Saas Auction.

We are pleased to inform you that your membership registration has been completed.
Please use the email address and password you provided during the application process to log in.

{0}',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (
        SELECT constant_no
        FROM m_constant WHERE value_name = 'メールテンプレート(会員申請: 管理者向け)'
    ),
    'ja',
    'Saas Auction　会員申請がありました',
    '<EMAIL>', -- FROM
    '<EMAIL>', -- BCC
    '{0}様から会員申請がありました。

【会員情報】
会社名：{1}
電話番号：{2}
メールアドレス：{3}',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (
        SELECT constant_no
        FROM m_constant WHERE value_name = 'メールテンプレート(会員申請: 管理者向け)'
    ),
    'en',
    'Saas Auction membership application has been received',
    '<EMAIL>', -- FROM
    '<EMAIL>', -- BCC
    'There has been a membership application from {0}.

【User Information】
Company name: {1}
Tel: {2}
Email: {3}',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (
        SELECT constant_no
        FROM m_constant WHERE value_name = 'メールテンプレート(会員申請: 会員向け)'
    ),
    'ja',
    'Saas Auction　新規会員登録のお知らせ',
    '<EMAIL>', -- FROM
    '',
    'Saas Auctionに会員申請いただきありがとうございます。
会員申請を受け付けましたのでお知らせいたします。

【ご注意】
まだ登録は仮の状態です。
今しばらくお待ちくださいますよう、よろしくお願い申し上げます。

{0}',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (
        SELECT constant_no
        FROM m_constant WHERE value_name = 'メールテンプレート(会員申請: 会員向け)'
    ),
    'en',
    'Saas Auction Thank you very much for your application for membership registration',
    '<EMAIL>', -- FROM
    '',
    'Thank you for applying for membership with Saas Auction.
We are pleased to inform you that your membership application has been received.

[Important Notice]
Your registration is currently in a provisional state.
We kindly ask for your patience as we process your application.

{0}',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (
        SELECT constant_no
        FROM m_constant WHERE value_name = 'メールテンプレート(会員退会: 管理者向け)'
    ),
    'ja',
    'Saas Auction　会員が退会しました',
    '<EMAIL>', -- FROM
    '<EMAIL>', -- BCC
    '{0}様が退会しました。

【会員情報】
会員ID：{1}
会社名：{2}
電話番号：{3}
メールアドレス：{4}',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (
        SELECT constant_no
        FROM m_constant WHERE value_name = 'メールテンプレート(会員退会: 管理者向け)'
    ),
    'en',
    'Saas Auction: Member withdrawn notification',
    '<EMAIL>', -- FROM
    '<EMAIL>', -- BCC
    '{0} has withdrawn their membership.

[Membership Information]
Member ID: {1}
Company Name: {2}
Tel: {3}
Email Address: {4}',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (
        SELECT constant_no
        FROM m_constant WHERE value_name = 'メールテンプレート(会員退会: 会員向け)'
    ),
    'ja',
    'Saas Auction　退会のお知らせ',
    '<EMAIL>', -- FROM
    '',
    'Saas Auctionをご利用いただきありがとうございました。
退会を受け付けましたのでお知らせいたします。
また機会がございましたら是非ご利用ください。

{0}',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (
        SELECT constant_no
        FROM m_constant WHERE value_name = 'メールテンプレート(会員退会: 会員向け)'
    ),
    'en',
    'Saas Auction Thank you for giving us the opportunity to be of service to you.',
    '<EMAIL>', -- FROM
    '',
    'Thank you for giving us the opportunity to be of service to you.
We are sure there will be other occasions.

{0}',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = 'TOP交代のお知らせ'),
    'ja',
    'Saas Auction　最高値更新のお知らせ',
    '<EMAIL>', -- FROM
    '',
    'Saas Auctionへのご参加ありがとうございます。以下の商品に他の会員様からより高い入札がありました。落札するには金額を上げて再入札をお願いします。

商品名：{0}
現在価格：{1}

詳細は以下のURLよりご確認ください。
https://saas-1-auction.stage.auction.custom-ec.com/details/{2}

{3}',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = 'TOP交代のお知らせ'),
    'en',
    'Saas Auction The top status has changed',
    '<EMAIL>', -- FROM
    '',
    'Regarding the bids for the following product, other members have made higher bids, so please raise the amount and re-bid to make a successful bid.

【Bid details】
Product name: {0}
Current price: {1}

Please check the details from the following URL.
https://saas-1-auction.stage.auction.custom-ec.com/details/{2}

{3}',
    ''
);
-- オークション方式がテンダー形式の場合に使用する通知メール。使用なし
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '入札会通知メール内容'),
    'ja',
    '入札会通知メール内容',
    '<EMAIL>', -- FROM
    '',
    '

  <div class="sec1__flex">
    <div class="flexdetail">
      <a href="{0}">
      <img class="machine" src="{1}" alt="">
      <ul>
        <li class="list_top">Lot No.:{2}</li>
        <li class="list">Model:{3}</li>
        <li class="list">S/No.:{4}</li>
        <li class="list">YOM:{5}</li>
		<li class="pricered-label"><p>{6}</p></li>
        <li class="pricered"><p>{7}円</p></li>
        <li class="todetail"><p><img class="yajirushi" src="##STATIC_URL/images/btn.png" alt=""></p></li>
      </ul>
      </a>
    </div>
  </div>

',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '入札会通知メール内容'),
    'en',
    '入札会通知メール内容',
    '<EMAIL>', -- FROM
    '',
    '

  <div class="sec1__flex">
    <div class="flexdetail">
      <a href="{0}">
      <img class="machine" src="{1}" alt="">
      <ul>
        <li class="list_top">Lot No.:{2}</li>
        <li class="list">Model:{3}</li>
        <li class="list">S/No.:{4}</li>
        <li class="list">YOM:{5}</li>
		<li class="pricered-label"><p>{6}</p></li>
        <li class="pricered"><p>{7}yen</p></li>
        <li class="todetail"><p><img class="yajirushi" src="##STATIC_URL/images/btn.png" alt=""></p></li>
      </ul>
      </a>
    </div>
  </div>

',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '言語(英語)'),
    'common',
    'en',
    '英語',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '言語(日本語)'),
    'common',
    'ja',
    '日本語',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '会員ステータス（未対応）'),
    'ja',
    '0',
    '未対応',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '会員ステータス（未対応）'),
    'en',
    '0',
    'Not processed',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '会員ステータス（承認）'),
    'ja',
    '1',
    '承認',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '会員ステータス（承認）'),
    'en',
    '1',
    'Approved',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '会員ステータス（非承認）'),
    'ja',
    '2',
    '非承認',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '会員ステータス（非承認）'),
    'en',
    '2',
    'Rejected',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '会員ステータス（一時停止）'),
    'ja',
    '8',
    '一時停止',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '会員ステータス（一時停止）'),
    'en',
    '8',
    'Temporarily Suspended',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '会員ステータス（退会）'),
    'ja',
    '9',
    '退会',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '会員ステータス（退会）'),
    'en',
    '9',
    'Withdrawn',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = 'NEWマーク表示期間'),
    'ja',
    '2',
    '',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = 'NEWマーク表示期間'),
    'en',
    '2',
    '',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = 'ピッチ幅(1)'),
    'ja',
    '1',
    'USD',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = 'ピッチ幅(1)'),
    'en',
    '1',
    'USD',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = 'おすすめ表示期間'),
    'ja',
    '7',
    '',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = 'おすすめ表示期間'),
    'en',
    '7',
    '',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = 'SOLD OUT表示期間'),
    'ja',
    '7',
    '',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = 'SOLD OUT表示期間'),
    'en',
    '7',
    '',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = 'お知らせメール内容'),
    'ja',
    'お知らせメール内容',
    '<EMAIL>', -- FROM
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = 'お知らせメール内容'),
    'en',
    'お知らせメール内容',
    '<EMAIL>', -- FROM
    '',
    '',
    ''
);
-- メールお知らせ重要度 --
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = 'メール重要度(低)'),
    'ja',
    '1',
    '低',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = 'メール重要度(低)'),
    'en',
    '1',
    'Low',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = 'メール重要度(中)'),
    'ja',
    '5',
    '中',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = 'メール重要度(中)'),
    'en',
    '5',
    'Normal',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = 'メール重要度(高)'),
    'ja',
    '9',
    '高',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = 'メール重要度(高)'),
    'en',
    '9',
    'High',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = 'お知らせ区分(普通)'),
    'ja',
    '1',
    '普通',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = 'お知らせ区分(普通)'),
    'en',
    '1',
    'Normal',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = 'お知らせ区分(重要)'),
    'ja',
    '3',
    '重要',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = 'お知らせ区分(重要)'),
    'en',
    '3',
    'Emergency',
    '',
    '',
    ''
);

-- 入札会メール --
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '入札会メール(入札会案内)'),
    'en',
    '1',
    '入札会案内',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '入札会メール(入札会開始)'),
    'en',
    '2',
    '入札会開始',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '入札会メール(入札会中盤)'),
    'en',
    '3',
    '入札会中盤',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '入札会メール(入札会終了間近)'),
    'en',
    '4',
    '入札会終了間近',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '入札会メール(入札会案内)'),
    'ja',
    '1',
    '入札会案内',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '入札会メール(入札会開始)'),
    'ja',
    '2',
    '入札会開始',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '入札会メール(入札会中盤)'),
    'ja',
    '3',
    '入札会中盤',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '入札会メール(入札会終了間近)'),
    'ja',
    '4',
    '入札会終了間近',
    '',
    '',
    ''
);

-- 商品の属性 --
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '商品の属性(家電)'),
    'ja',
    '1',
    '家電',
    '',
    '0,0',
    '#ff00c0'
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '商品の属性(オーディオ機器)'),
    'ja',
    '2',
    'オーディオ機器',
    '',
    '0,0',
    '#ff0000'
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '商品の属性(カメラ)'),
    'ja',
    '3',
    'カメラ',
    '',
    '0,0',
    '#00ff00'
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '商品の属性(光学機器)'),
    'ja',
    '4',
    '光学機器',
    '',
    '0,0',
    '#0000ff'
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '商品の属性(映像機器)'),
    'ja',
    '5',
    '映像機器',
    '',
    '0,0',
    '#ffff00'
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '商品の属性(楽器)'),
    'ja',
    '6',
    '楽器',
    '',
    '0,0',
    '#ff00ff'
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '商品の属性(コンピュータパーツ)'),
    'ja',
    '7',
    'コンピュータパーツ',
    '',
    '0,0',
    '#00ffff'
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '商品の属性(コンピュータサプライ)'),
    'ja',
    '8',
    'コンピュータサプライ',
    '',
    '0,0',
    '#800000'
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '商品の属性(コンピュータ周辺機器)'),
    'ja',
    '9',
    'コンピュータ周辺機器',
    '',
    '0,0',
    '#008000'
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '商品の属性(ホビーその他)'),
    'ja',
    '10',
    'ホビーその他',
    '',
    '0,0',
    '#000080'
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '商品の属性(機材)'),
    'ja',
    '11',
    '機材',
    '',
    '0,0',
    '#808000'
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '商品の属性(その他)'),
    'ja',
    '99',
    'その他',
    '',
    '0,0',
    '#ffc080'
);
-- 入札単位区分 --
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '入札単位区分(入札金額帯)'),
    'ja',
    '0',
    '入札金額帯',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '入札単位区分(指定単位)'),
    'ja',
    '1',
    '指定単位',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '入札単位区分(入札金額帯)'),
    'en',
    '0',
    'Following Current Price',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '入札単位区分(指定単位)'),
    'en',
    '1',
    'Pre-input',
    '',
    '',
    ''
);
-- 終了区分 --
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '終了区分(随時終了)'),
    'ja',
    '0',
    '現在価格通り',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '終了区分(一斉終了)'),
    'ja',
    '1',
    '一斉終了',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '終了区分(随時終了)'),
    'en',
    '0',
    'One by one',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '終了区分(一斉終了)'),
    'en',
    '1',
    'All ends',
    '',
    '',
    ''
);
-- 税率 --
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '税率(10)'),
    'en',
    '10',
    '10%',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '税率(10)'),
    'ja',
    '10',
    '10%',
    '',
    '',
    ''
);
-- PITCH入札金額帯の時 --
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '入札金額帯(50,000以下)'),
    'ja',
    '500',
    '50,000以下',
    '0',
    '50000',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '入札金額帯(50,001～500,000)'),
    'ja',
    '1000',
    '50,001～500,000',
    '50000',
    '500000',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '入札金額帯(500,001～)'),
    'ja',
    '5000',
    '500,001～',
    '500000',
    '',
    ''
);

INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '入札金額帯(50,000以下)'),
    'en',
    '500',
    '50,000以下',
    '0',
    '50000',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '入札金額帯(50,001～500,000)'),
    'en',
    '1000',
    '50,001～500,000',
    '50000',
    '500000',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '入札金額帯(500,001～)'),
    'en',
    '5000',
    '500,001～',
    '500000',
    '',
    ''
);

-- 延長フラグ --
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '延長フラグ'),
    'ja',
    '1', -- 0：無 1：有
    '',
    '',
    '',
    ''
);

-- 最大延長分 --
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '最大延長時間（分）'),
    'ja',
    '180', -- 終了時刻の3時間後
    '',
    '',
    '',
    ''
);

-- 延長開始判定分 --
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '延長開始判定分'),
    'ja',
    '5', -- 終了時刻の5分前
    '',
    '',
    '',
    ''
);

-- 延長加算分数 --
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '延長加算分数'),
    'ja',
    '5', -- 入札があった場合に5分加算
    '',
    '',
    '',
    ''
);

-- あと少し表示有無 --
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = 'あと少し表示有無'),
    'ja',
    '1', -- 0：無 1：有
    '',
    '',
    '',
    ''
);

-- 商品のカテゴリ --
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '商品のカテゴリー(iOS)'),
    'ja',
    '1',
    'iOS',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '商品のカテゴリー(Android)'),
    'ja',
    '2',
    'Android',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '商品のカテゴリー(その他)'),
    'ja',
    '3',
    'その他',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '商品のカテゴリー(iOS)'),
    'en',
    '1',
    'iOS',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '商品のカテゴリー(Android)'),
    'en',
    '2',
    'Android',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '商品のカテゴリー(その他)'),
    'en',
    '3',
    'Others',
    '',
    '',
    ''
);

-- 送料 --
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '送料(80)'),
    'ja',
    '80',
    '950',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '送料(100)'),
    'ja',
    '100',
    '1580',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '送料(120)'),
    'ja',
    '120',
    '2080',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '送料(150)'),
    'ja',
    '150',
    '2980',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '送料(160)'),
    'ja',
    '160',
    '3600',
    '',
    '',
    ''
);

-- 検索条件日時From --
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = '検索条件日時From'),
    'ja',
    '30',
    '',
    '',
    '',
    ''
);

-- 「お気に入り」中の商品の終了予定時間N分前に会員にメール送信用定数
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE key_string = 'AUCTION_END_COUNTDOWN_CONSTANT'),
    'ja',
    '30',
    'minute',
    '',
    '',
    ''
);

-- 管理側での会員登録時のメール送信
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1, --Title
    value2, -- FROM
    value3,
    value4, -- 本文
    value5  --
) VALUES(
    1,
    (
        SELECT constant_no
        FROM m_constant WHERE value_name = 'メールテンプレート(管理側で会員登録情報送信: 会員向け)'
    ),
    'ja', -- language_code
    'Saas Auction　会員登録完了のお知らせ', --Title
    '<EMAIL>', -- FROM
    '',
    'Saas Auctionにお申し込みいただきありがとうございます。
会員登録が完了しましたのでお知らせいたします。

仮パスワードは以下となります。
{0}

ログインIDは登録のメールアドレスをご入力ください。

{1}',
''
);

INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1, --Title
    value2, -- FROM
    value3,
    value4, -- 本文
    value5  --
) VALUES(
    1,
    (
        SELECT constant_no
        FROM m_constant WHERE value_name = 'メールテンプレート(管理側で会員登録情報送信: 会員向け)'
    ),
    'en', -- language_code
    'Saas Auction Your account has been successfully created', --Title
    '<EMAIL>', -- FROM
    '',
    'Thank you for signing up for Saas Auction.
We are pleased to inform you that your membership registration has been completed.

Your temporary password is as follows:
{0}

Please use your registered email address as your login ID.

{1}',
''
);

-- Topページでお知らせ開始日から3日以内のお知らせだけ「NEW 」マークを表示する
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (
        SELECT constant_no
        FROM m_constant WHERE value_name = 'お知らせ(NEW)'
    ),
    'ja',
    '3',
    '',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (
        SELECT constant_no
        FROM m_constant WHERE value_name = 'お知らせ(NEW)'
    ),
    'en',
    '3',
    '',
    '',
    '',
    ''
);

-- Login token expiration
INSERT INTO m_constant_localized(tenant_no, constant_no, language_code,value1,value2,value3,value4,value5) values(1, (SELECT constant_no FROM m_constant WHERE value_name = '会員登録トークン(有効期限時間)'), 'en', '24', '', '' ,'' ,'');
INSERT INTO m_constant_localized(tenant_no, constant_no, language_code,value1,value2,value3,value4,value5) values(1, (SELECT constant_no FROM m_constant WHERE value_name = '会員登録トークン(有効期限時間)'), 'ja', '24', '', '' ,'' ,'');

-- Product rank pdf
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (
        SELECT constant_no
        FROM m_constant WHERE value_name = '商品ランクPDF(A)'
    ),
    'en',
    'A',
    '',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (
        SELECT constant_no
        FROM m_constant WHERE value_name = '商品ランクPDF(B)'
    ),
    'en',
    'B',
    '',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (
        SELECT constant_no
        FROM m_constant WHERE value_name = '商品ランクPDF(C)'
    ),
    'en',
    'C',
    '',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (
        SELECT constant_no
        FROM m_constant WHERE value_name = '商品ランクPDF(J)'
    ),
    'en',
    'J',
    '',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (
        SELECT constant_no
        FROM m_constant WHERE value_name = '商品ランクPDF(A)'
    ),
    'ja',
    'A',
    '',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (
        SELECT constant_no
        FROM m_constant WHERE value_name = '商品ランクPDF(B)'
    ),
    'ja',
    'B',
    '',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (
        SELECT constant_no
        FROM m_constant WHERE value_name = '商品ランクPDF(C)'
    ),
    'ja',
    'C',
    '',
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (
        SELECT constant_no
        FROM m_constant WHERE value_name = '商品ランクPDF(J)'
    ),
    'ja',
    'J',
    '',
    '',
    '',
    ''
);

-- メールFROM --
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE key_string = 'EMAIL_FROM'),
    'ja', -- language_code
    'sender', -- value1
    '<EMAIL>', -- value 2
    '',
    '',
    ''
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE value_name = 'メールFROM(sender)'),
    'en',
    'sender',
    '<EMAIL>',
    '',
    '',
    ''
);

-- 入札開始メール --
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (
        SELECT constant_no
        FROM m_constant WHERE value_name = 'メールテンプレート(オークション入札開始: 会員向け)'
    ),
    'ja',
    'Saas Auction　オークション開催のお知らせ',
    '<EMAIL>', -- FROM
    '',
    'Saas Auctionへのご参加ありがとうございます。

以下のオークションの入札受付を開始しましたのでお知らせいたします。

オークション名：{0}

{1}',
    'オークション名'
);
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (
        SELECT constant_no
        FROM m_constant WHERE value_name = 'メールテンプレート(オークション入札開始: 会員向け)'
    ),
    'en',
    'Saas Auction Notification of Auction Opening',
    '<EMAIL>', -- FROM
    '',
    'Thank you for participating in Saas Auction.

We are pleased to inform you that bidding has opened for the following auction:

Auction Name: {0}

{1}',
    'オークション名'
);

INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (
        SELECT constant_no
        FROM m_constant WHERE value_name = 'メールテンプレート(会員情報確認: 管理者向け)'
    ),
    'ja',
    '【アカウント確認】管理者ユーザーの有効化を完了してください',
    '<EMAIL>', -- FROM
    '<EMAIL>', -- BCC
    'このメールは管理者アカウントの確認用です。
以下のリンクをクリックしてアカウントを有効化してください。
【リンク】
{1}
',
    ''
);

-- Localized values for self-approval confirmation email
-- Japanese
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE tenant_no = 1 AND key_string = 'EMAIL_MEMBER_SELF_APPROVAL_CONFIRM'),
    'ja',
    '【Saas Auction】会員登録の確認', -- title
    '', -- use key_string : EMAIL_FROM
    '',
    '以下のリンクをクリックして会員登録を完了してください。

確認リンク: {1}

{0}', -- body
    ''
);

-- English
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE tenant_no = 1 AND key_string = 'EMAIL_MEMBER_SELF_APPROVAL_CONFIRM'),
    'en',
    'Saas Auction: Confirm your membership', -- title
    '', -- use key_string : EMAIL_FROM
    '',
    'Please click the link below to complete your registration.

Confirm link: {1}

{0}', -- body
    ''
);

-- OTP Authentication Code Email Template
-- Japanese
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE tenant_no = 1 AND key_string = 'EMAIL_OTP_AUTH_CODE'),
    'ja',
    '【Saas Auction】ログイン認証コードのご案内', -- subject
    '', -- use key_string : EMAIL_FROM
    '',
    'Saas Auctionをご利用いただき誠にありがとうございます。

下記の認証コードをご利用いただき、ログインを完了してください。

認証コード: {1}

※このコードは一定時間のみ有効です。有効期限を過ぎた場合は、再度ログインをお試しください。
※本メールに心当たりがない場合は、破棄してください。

{0}', -- body
    ''
);

-- English
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE tenant_no = 1 AND key_string = 'EMAIL_OTP_AUTH_CODE'),
    'en',
    '[Saas Auction] Your One-Time Authentication Code', -- subject
    '', -- use key_string : EMAIL_FROM
    '',
    'Thank you for using Saas Auction.

Please use the authentication code below to complete your login:

Authentication Code: {1}

※ This code is only valid for a limited time. If it has expired, please request a new login attempt.
※ If you did not request this email, please disregard it.

{0}', -- body
    ''
);

-- ADMIN_CREATE_MEMBER_PASSWORD_RESET Japanese
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE tenant_no = 1 AND key_string = 'ADMIN_CREATE_MEMBER_PASSWORD_RESET'),
    'ja',
    '【Saas Auction】初回パスワード設定のご案内', -- subject
    'https://saas-1-auction.stage.auction.custom-ec.com', -- reset link
    '', -- bcc
    'Saas Auctionをご利用いただき誠にありがとうございます。

管理者によってアカウントが作成されました。
ログインをするためには、初回パスワードの設定が必要です。

下記のリンクをクリックして、パスワードを設定してください：
{0}

※このリンクは一定期間のみ有効です。有効期限を過ぎた場合は、管理者にお問い合わせください。
※本メールに心当たりがない場合は、破棄してください。

{1}', -- body (0: reset link, 1: footer)
    ''
);

-- ADMIN_CREATE_MEMBER_PASSWORD_RESET English
INSERT INTO m_constant_localized(
    tenant_no,
    constant_no,
    language_code,
    value1,
    value2,
    value3,
    value4,
    value5
) VALUES(
    1,
    (SELECT constant_no FROM m_constant WHERE tenant_no = 1 AND key_string = 'ADMIN_CREATE_MEMBER_PASSWORD_RESET'),
    'en',
    '[Saas Auction] Please Set Your Initial Password', -- subject
    'https://saas-1-auction.stage.auction.custom-ec.com', -- reset link
    '', -- bcc
    'Thank you for using Saas Auction.

Your account has been created by an administrator.
To log in, you need to set your initial password.

Please click the link below to set your password:
{0}

※ This link is only valid for a limited time. If it has expired, please contact the administrator.
※ If you did not request this email, please disregard it.

{1}', -- body (0: reset link, 1: footer)
    ''
);
