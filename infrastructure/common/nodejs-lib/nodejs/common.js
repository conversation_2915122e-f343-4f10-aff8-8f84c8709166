const moment = require('moment')
const {InvokeCommand, LambdaClient, LogType} = require('@aws-sdk/client-lambda')
const jwt = require('jsonwebtoken')

const MailComposer = require('nodemailer/lib/mail-composer')
const {
  SendEmailCommand,
  SendRawEmailCommand,
  SESClient,
} = require('@aws-sdk/client-ses')
const {extractFromCognitoGroups} = require('./cognitoUtils.js')

module.exports = {
  /**
   * レスポンス作成
   * @param {object} cb コールバック関数
   * @param {object} response レスポンス
   * @returns {object} コールバック関数を呼ぶ
   */
  createSuccessResponse(cb, response) {
    return cb(null, response || {})
  },
  /**
   * Errorからレスポンス作成
   * @param cb
   * @param {object} error Error
   * @returns {object} エラー内容
   */
  createErrorResponse(cb, error) {
    console.log('error', error)
    return Promise.resolve()
      .then(() => {
        return this.noticeToSlack(error)
      })
      .then(result => {
        return cb(null, null)
      })
      .catch(error => {
        console.log(error)
        return cb(null, null)
      })
  },
  noticeToSlack(error) {
    const message = `システムエラーが発生しました。\n  - リクエスト名：${process.env.AWS_LAMBDA_FUNCTION_NAME
      } \n  - エラー内容：${error
        ? error.message || JSON.stringify(error)
        : 'エラーメッセージが取得できません。'
      }`
    return this.invokeLambda(process.env.NOTICE_SLACK_FUNCTION, {
      message,
    }).catch(error => {
      console.log(error)
      return Promise.resolve()
    })
  },
  invokeLambda(functionArn, event) {
    const client = new LambdaClient({})
    const command = new InvokeCommand({
      FunctionName: functionArn,
      Payload: JSON.stringify(event),
      InvocationType: 'RequestResponse',
      LogType: LogType.Tail,
    })
    return client.send(command).then(({Payload, LogResult}) => {
      const payloadString = Buffer.from(Payload).toString('utf-8')
      const payload = JSON.parse(payloadString)

      const logs = LogResult ? Buffer.from(LogResult, 'base64').toString('utf-8') : ''

      console.log('Payload: ', payload)
      console.log('Logs: ', logs)

      if (payload.errorType || payload.errorMessage) {
        return reject(payload.errorMessage)
      }
      return Promise.resolve(payload)
    })
  },
  invokeLambdaEventType(functionArn, body, options = {}) {
    const client = new LambdaClient(options)
    const command = new InvokeCommand({
      FunctionName: functionArn,
      Payload: JSON.stringify({body}),
      InvocationType: 'Event',
      LogType: LogType.Tail,
    })
    return client.send(command).then(result => {
      return Promise.resolve(result)
    })
  },
  /**
   * Randomの文字列を作成する
   * @param length 文字列の長さ
   */
  randomString(length) {
    let result = ''
    const characters =
      'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
    const charactersLength = characters.length
    for (let i = 0; i < length; i++) {
      result += characters.charAt(Math.floor(Math.random() * charactersLength))
    }
    return result
  },
  randomNumber(length) {
    let result = ''
    const characters = '0123456789'
    const charactersLength = characters.length
    for (let i = 0; i < length; i++) {
      result += characters.charAt(Math.floor(Math.random() * charactersLength))
    }
    return result
  },
  sendMailBySES(title, content, sender, receivers, bcc = []) {
    const sesClient = new SESClient({
      region: 'ap-northeast-1',
    })
    if (sender && receivers) {
      console.log('sendMailBySES', title, content, sender, receivers)
      const input = {
        Source: sender,
        Destination: {
          ToAddresses: receivers,
          BccAddresses: bcc,
          CcAddresses: [],
        },
        Message: {
          Body: {
            Text: {
              Charset: 'UTF-8',
              Data: content,
            },
          },
          Subject: {
            Charset: 'UTF-8',
            Data: title,
          },
        },
      }
      const command = new SendEmailCommand(input)
      return sesClient.send(command)
    }
    console.log('sender, recievers is null.')
    return Promise.resolve(null)
  },
  sendRawMailBySES(
    title,
    content,
    sender,
    receivers,
    bcc = [],
    attachments = [],
    htmlMail = false
  ) {
    const sesClient = new SESClient({
      region: 'ap-northeast-1',
    })
    const destinations = [...receivers, ...bcc]
    return new MailComposer({
      from: sender,
      subject: title,
      text: htmlMail ? null : content,
      html: htmlMail ? content : null,
      bcc,
      to: receivers,
      attachments,
    })
      .compile()
      .build()
      .then(data => {
        if (sender && receivers) {
          console.log('sendRawMailBySES', title, destinations)
          const command = new SendRawEmailCommand({
            Source: sender,
            Destinations: destinations,
            RawMessage: {
              Data: data,
            },
          })
          return sesClient.send(command)
        }
        console.log('sender, recievers is null.')
        return Promise.resolve(null)
      })
  },
  replaceURLs(message) {
    if (!message) {
      return null
    }
    const urlRegex = /(((https?:\/\/)|(www\.))[^\s]+)/g
    return message.replace(urlRegex, url => {
      let hyperlink = url
      if (!hyperlink.match('^https?://')) {
        hyperlink = `http://${hyperlink}`
      }
      return `<a href="${hyperlink
        }" target="_blank" rel="noopener noreferrer">${url}</a>`
    })
  },
  // Timestamp with time zoneをYYYY-MM-DDの形で返す
  getFormatDate(date, format = 'YYYY/MM/DD') {
    return date ? moment(date).format(format) : ''
  },
  // Timestamp with time zoneをHH-mmの形で返す
  getFormatTime(date, format = 'HH:mm') {
    return date ? moment(date).format(format) : ''
  },
  // Timestamp with time zoneをYYYY/MM/DD HH:mmの形で返す
  getFormatDateTime(date) {
    return date ? moment(date).format('YYYY/MM/DD HH:mm') : ''
  },
  // Send requests using axios
  async sendRequest(
    url,
    body = {},
    headers = {'Content-Type': 'application/json'}
  ) {
    const axios = require('axios')
    const response = await axios.post(url, body, {headers})
    console.log(response.data)
    return response.data
  },
  sqlParamNumber(length) {
    return Array.from({length}, (v, i) => `$${i + 1}`)
  },
  dateToString(date) {
    if (!date) {
      return ''
    }
    return [
      date.getFullYear(),
      `0${date.getMonth() + 1}`.slice(-2),
      `0${date.getDate()}`.slice(-2),
      `0${date.getHours()}`.slice(-2),
      `0${date.getMinutes()}`.slice(-2),
      `0${date.getSeconds()}`.slice(-2),
    ].join('')
  },
  numberStringWithComma(val) {
    if (isNaN(val)) {
      return val
    }
    return Number.parseInt(val, 10)
      .toFixed(0)
      .replace(/(\d)(?=(\d{3})+$)/g, '$1,')
  },
  /**
   * 指定された文字列に、引数配列の値を埋め込みます。
   * @param {string} val - プレースホルダーを含む文字列（例: "{0}の{1}タイトルを入力してください。"）
   * @param {string[]} args - プレースホルダーに差し込む値の配列（例: ["ユーザー名", "タイトル"]）
   * @returns {string} プレースホルダーが置換された結果の文字列
   * @example
   * Common.format('{0}の{1}タイトルを入力してください。', ['ユーザー名', 'タイトル']);
   * // → 'ユーザー名のタイトルタイトルを入力してください。'
   */
  format(val, args) {
    let a = val
    for (const k in args) {
      // k is index
      a = a.replace(`{${k}}`, args[k])
    }
    return a
  },
  sortBy(a, func = (x, y) => x < y) {
    return a.sort((x, y) => {
      if (func(x, y)) {
        return -1
      }
      return 1
    })
  },
  sleep(waitSeconds) {
    return new Promise(resolve => {
      setTimeout(() => {
        resolve()
      }, waitSeconds * 1000)
    })
  },
  splitAll(promise, argumentList, splitAmount, waitSeconds = null) {
    if (argumentList.length === 0) {
      return Promise.resolve([])
    }
    const runSplitPromise = (args, index, resultArray) => {
      if (index < args.length) {
        return Promise.all(
          args[index].map(x => {
            return Promise.resolve()
              .then(() => {
                if (waitSeconds && index !== 0) {
                  return this.sleep(waitSeconds)
                }
                return Promise.resolve()
              })
              .then(() => {
                return promise(...x)
              })
          })
        ).then(result => {
          Array.prototype.push.apply(resultArray, result)
          return runSplitPromise(args, index + 1, resultArray)
        })
      }
      return Promise.resolve(resultArray)
    }

    const splitNumberOfTimes = Math.ceil(argumentList.length / splitAmount)
    return runSplitPromise(
      Array(splitNumberOfTimes)
        .fill({})
        .map((tmp, index) => {
          return argumentList.slice(
            index * splitAmount,
            (index + 1) * splitAmount
          )
        }),
      0,
      []
    )
  },
  dateToStringWithDelimiter(date) {
    return [
      date.getFullYear(),
      '-',
      `0${date.getMonth() + 1}`.slice(-2),
      '-',
      `0${date.getDate()}`.slice(-2),
      ' ',
      `0${date.getHours()}`.slice(-2),
      ':',
      `0${date.getMinutes()}`.slice(-2),
      ':',
      `0${date.getSeconds()}`.slice(-2),
    ].join('')
  },
  // ファイル名をパースする。
  // ファイル名が「fff/aaa.bbb.ccc」の場合、{name: 'aaa.bbb', ext: 'ccc'} というオブジェクトを返す。
  parseFileName(filePath) {
    // Get the file name from the path
    const fileName = filePath.split('/').pop()
    // Find the last dot for separating name and extension
    const lastDotIndex = fileName.lastIndexOf('.')
    // No extension found
    if (lastDotIndex === -1) {
      return {name: fileName, ext: ''}
    }
    // Part before the last dot
    const name = fileName.slice(0, lastDotIndex)
    // Part after the last dot
    const ext = fileName.slice(lastDotIndex + 1)
    return {name, ext}
  },
  // Check if the object is empty
  isEmpty(val) {
    let bool = false
    // Undefined
    if (typeof val === 'undefined') {
      bool = true
      // Empty string
    } else if (typeof val === 'string') {
      if (val === '') {
        bool = true
      }
      // Null or empty object or empty array
    } else if (typeof val === 'object') {
      if (val === null) {
        bool = true
      } else {
        let ctr = 0
        for (const k in val) {
          ctr++
        }
        if (ctr <= 0) {
          bool = true
        }
      }
    }
    return bool
  },
  /**
   * Parses the request body if it’s JSON, otherwise returns it as-is.
   * @param {string|object} body - The raw request body from the event.
   * @returns {object|string} - Parsed JSON object or original body.
   */
  parseRequestBody(body) {
    if (typeof body === 'string') {
      try {
        return JSON.parse(body)
      } catch (error) {
        return body
      }
    }
    // If body is already an object (e.g., from non-JSON content), use as-is
    return body
  },
  getCognitoGroupPrefix() {
    return 'tenant-id:'
  },
  isNullish(val) {
    return val === null || val === undefined
  },
  // 空文字列かどうかをチェックする。(.length > 0 is ok)
  isBlank(val) {
    return val === null || val === undefined || String(val).trim() === ''
  },
  /**
   * Request から tenant ID を抽出する
   * Authenticated users: Cognito claims から抽出する. exg. custom:tenant_id:1
   * Unauthenticated users: ホスト名またはクエリパラメータから抽出する
   * @param {object} event - Lambda イベントオブジェクト
   * @returns {number|string} - テナント ID, exg. 1
   */
  extractTenantId(event) {
    const claims = event?.requestContext?.authorizer?.claims
    console.log(
      'cognito claims(extractTenantId): ',
      JSON.stringify(claims, null, 2)
    )
    if (claims) {
      const result = extractFromCognitoGroups(event, 'tenantId')
      if (result.error || !result.data) {
        this.noticeToSlack(result)
        return {
          status: 500,
          message: result.message || 'Failed to extract Tenant ID from claims',
        }
      }
      return result.data
    }
    // TODO: Refactor to use  base.checkOrigin() instead of custom tenant-resolver.js
    const tenantResolver = require('./tenant-resolver.js')
    const result = tenantResolver.resolveTenant(event)
    if (result.error || !result.data) {
      this.noticeToSlack(result)
      return {
        status: 500,
        message: result.message || 'Failed to resolve Tenant ID',
      }
    }
    return result.data
  },

  // TODO :use this , remove method in infrastructure/common/admin-side/gateway-resource/common-layer/nodejs/base.js
  extractAdminNo(event) {
    const result = extractFromCognitoGroups(event, 'adminNo')
    if (result.error || !result.data) {
      this.noticeToSlack(result)
      return {message: 'Failed to extract Admin No from claims'}
    }
    return result.data
  },

  extractMemberNo(event) {
    const result = extractFromCognitoGroups(event, 'memberNo')
    if (result.error || !result.data) {
      this.noticeToSlack(result)
      return {message: 'Failed to extract Member No from claims'}
    }
    return result.data
  },

  extractUserNo(event) {
    const result = extractFromCognitoGroups(event, 'userNo')
    if (result.error || !result.data) {
      this.noticeToSlack(result)
      return {message: 'Failed to extract User No from claims'}
    }
    return result.data
  },

  extractMemberName(event) {
    const result = extractFromCognitoGroups(event, 'memberName')
    if (result.error || !result.data) {
      this.noticeToSlack(result)
      return {message: 'Failed to extract Member Name from claims'}
    }
    return result.data
  },

  extractAdminLanguageCode(event) {
    const result = extractFromCognitoGroups(event, 'adminLanguageCode')
    if (result.error || !result.data) {
      this.noticeToSlack(result)
      return {message: 'Failed to extract Admin Language Code from claims'}
    }
    return result.data
  },

  extractRoleId(event) {
    const result = extractFromCognitoGroups(event, 'roleId')
    if (result.error || !result.data) {
      this.noticeToSlack(result)
      return {message: 'Failed to extract Role ID from claims'}
    }
    return result.data
  },

  extractAdminName(event) {
    const result = extractFromCognitoGroups(event, 'adminName')
    if (result.error || !result.data) {
      this.noticeToSlack(result)
      return {message: 'Failed to extract Admin Name from claims'}
    }
    return result.data
  },

  // Function to extract member info from JWT token manually(use for public endpoint)
  extractMemberInfoFromToken(authHeader) {
    try {
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return null
      }

      const token = authHeader.replace('Bearer ', '')
      // Decode without verification for public endpoint
      const decoded = jwt.decode(token)
      console.log('☎️ log of decoded member info from token: ', decoded)

      if (decoded && decoded['custom:member_no']) {
        return {
          member_no: parseInt(decoded['custom:member_no'], 10),
          user_no: decoded['custom:user_no']
            ? parseInt(decoded['custom:user_no'], 10)
            : null,
          member_name: decoded['custom:member_name'] || null,
          email: decoded.email || null,
        }
      }
      return null
    } catch (error) {
      console.log('Error extracting member info from token:', error)
      return null
    }
  },
  // Determine if an error is a Cognito error based on common AWS Cognito exception names.
  isCognitoError(error) {
    if (!error || typeof error !== 'object') return false;
    const name = error.name || error.code;
    if (!name || typeof name !== 'string') return false;
    const cognitoErrorNames = new Set([
      // Sign up related
      'UsernameExistsException',
      'InvalidPasswordException',
      'InvalidParameterException',
      // Confirmation related
      'CodeMismatchException',
      'LimitExceededException',
      'ExpiredCodeException',
      'NotAuthorizedException',
      'CodeDeliveryFailureException',
      // Sign in related
      'UserNotConfirmedException',
      'PasswordResetRequiredException',
      'UserNotFoundException',
    ]);
    return cognitoErrorNames.has(name);
  },
  /**
   * Map a Cognito error to standardized API error response with error_type and code.
   * The response is intentionally code-centric so frontend can localize messages.
   * @param {any} error
   * @returns {{status:number, errors: {error_type:string, code:string, field:string}[]}}
   */
  mapCognitoError(error) {
    const name = error?.name || error?.code || 'UnknownError';
    const mapping = {
      // Sign up related
      UsernameExistsException: {code: 'AUTH_COGNITO_USERNAME_EXISTS_EXCEPTION', field: 'email'},
      InvalidPasswordException: {code: 'AUTH_COGNITO_INVALID_PASSWORD_EXCEPTION', field: 'password'},
      InvalidParameterException: {code: 'AUTH_COGNITO_INVALID_PARAMETER_EXCEPTION', field: 'general'},
      // Confirmation related
      CodeMismatchException: {code: 'AUTH_COGNITO_CODE_MISMATCH_EXCEPTION', field: 'verification_code'},
      LimitExceededException: {code: 'AUTH_COGNITO_LIMIT_EXCEEDED_EXCEPTION', field: 'general'},
      ExpiredCodeException: {code: 'AUTH_COGNITO_EXPIRED_CODE_EXCEPTION', field: 'verification_code'},
      NotAuthorizedException: {code: 'AUTH_COGNITO_NOT_AUTHORIZED_EXCEPTION', field: 'general'},
      CodeDeliveryFailureException: {code: 'AUTH_COGNITO_CODE_DELIVERY_FAILURE_EXCEPTION', field: 'general'},
      // Sign in related
      UserNotConfirmedException: {code: 'AUTH_COGNITO_USER_NOT_CONFIRMED_EXCEPTION', field: 'general'},
      PasswordResetRequiredException: {code: 'AUTH_COGNITO_PASSWORD_RESET_REQUIRED_EXCEPTION', field: 'password'},
      UserNotFoundException: {code: 'AUTH_COGNITO_USER_NOT_FOUND_EXCEPTION', field: 'general'},
    };

    const map = mapping[name] || {code: 'AUTH_COGNITO_COGNITO_UNKNOWN_ERROR', field: 'general'};
    return {
      status: 400,
      errors: [
        {
          error_type: 'cognito',
          code: map.code,
          field: map.field,
        },
      ],
    };
  }
}
