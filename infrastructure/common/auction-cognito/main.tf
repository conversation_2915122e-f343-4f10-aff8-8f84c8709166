resource "aws_cognito_user_pool" "auction_user_pool" {
  name = "${var.project_name}-${var.environment}-auction-side"

  # Allow users to sign in with email instead of username
  username_attributes      = ["email"]
  auto_verified_attributes = ["email"]

  # Password policy
  password_policy {
    minimum_length                   = var.password_policy.minimum_length
    require_lowercase                = var.password_policy.require_lowercase
    require_numbers                  = var.password_policy.require_numbers
    require_symbols                  = var.password_policy.require_symbols
    require_uppercase                = var.password_policy.require_uppercase
    temporary_password_validity_days = var.password_policy.temporary_password_validity_days
  }

  # Auction-specific custom attributes
  schema {
    name                = "member_no"
    attribute_data_type = "Number"
    mutable             = true
    required            = false
    number_attribute_constraints {
      min_value = "0"
    }
  }
  schema {
    name                = "user_no"
    attribute_data_type = "Number"
    mutable             = true
    required            = false
    number_attribute_constraints {
      min_value = "0"
    }
  }
  schema {
    name                = "member_name"
    attribute_data_type = "String"
    mutable             = true
    required            = false
    string_attribute_constraints {
      max_length = "100"
    }
  }
  # TODO: Remove , cause not used
  schema {
    name                = "language_code"
    attribute_data_type = "String"
    mutable             = true
    required            = false
    string_attribute_constraints {
      max_length = "2"
    }
  }

  # Email verification configuration
  verification_message_template {
    default_email_option = "CONFIRM_WITH_CODE"
    email_subject        = "[${var.project_name}] 検証コード"
    email_message        = "検証コードは「{####}」です。"
  }

  # MFA is disabled since we're using custom authentication flows
  # Custom auth triggers handle email verification challenges
  mfa_configuration = "OFF"

  account_recovery_setting {
    recovery_mechanism {
      name     = "verified_email"
      priority = 1
    }
  }

  lambda_config {
    pre_authentication             = module.auction-cognito-pre-auth.lambda_function_arn
    define_auth_challenge          = module.auction-cognito-define-challenge.lambda_function_arn
    create_auth_challenge          = module.auction-cognito-create-challenge.lambda_function_arn
    verify_auth_challenge_response = module.auction-cognito-verify-challenge.lambda_function_arn
    custom_message                 = module.auction-cognito-custom-message.lambda_function_arn
  }

}

# Create tenant groups for auction users
resource "aws_cognito_user_group" "auction_tenant_groups" {
  for_each     = toset(var.tenant_ids)
  name         = "tenant-id:${each.value}"
  user_pool_id = aws_cognito_user_pool.auction_user_pool.id
  description  = each.value == "0" || each.value == 0 ? "システム管理者用グループ" : "テナント${each.value}用グループ"
}

# Create app client for auction site
resource "aws_cognito_user_pool_client" "auction_client" {
  name                   = "auction-client"
  user_pool_id           = aws_cognito_user_pool.auction_user_pool.id
  generate_secret        = false
  refresh_token_validity = 1 # 1 day(maximum allowed for fresh token is 1day)
  access_token_validity  = 1 # 1 hour
  id_token_validity      = 1 # 1 hour

  callback_urls = var.callback_urls
  logout_urls   = var.logout_urls

  allowed_oauth_flows  = ["code", "implicit"]
  allowed_oauth_scopes = ["email", "openid", "profile"]

  # Security setting to prevent user enumeration attacks
  prevent_user_existence_errors = "ENABLED"

  # Authentication flows allowed for this client application
  explicit_auth_flows = [
    "ALLOW_REFRESH_TOKEN_AUTH",
    "ALLOW_USER_PASSWORD_AUTH",
    "ALLOW_ADMIN_USER_PASSWORD_AUTH",
    "ALLOW_USER_SRP_AUTH",
    "ALLOW_USER_AUTH",
    "ALLOW_CUSTOM_AUTH"
  ]
}

# Create domain for auction user pool
resource "aws_cognito_user_pool_domain" "auction_cognito_domain" {
  domain       = "${var.domain_prefix}-auction"
  user_pool_id = aws_cognito_user_pool.auction_user_pool.id
}


# IAM role for Cognito trigger lambdas (reuse default role module for baseline policies)
module "auction_cognito_lambda_role" {
  source                        = "../../modules/lambda-default-role"
  lambda_function_role_name     = "${var.environment}-${var.project_name}-auction-cognito-trigger-role"
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
}

# -----------------------
# Custom Auth Trigger Lambdas
# -----------------------

module "auction-cognito-pre-auth" {
  source                = "../../modules/default-lambda"
  description           = "Cognito PreAuthentication trigger"
  function_name         = "${var.project_name}-${var.environment}-auction-pre-auth"
  default-role-arn      = module.auction_cognito_lambda_role.arn
  lambda_layer          = [aws_lambda_layer_version.node_module_layer.arn, var.common_lambda_layer]
  subnet_ids            = var.lambda_subnet_ids
  security_group_ids    = [var.lambda_security_group_id]
  function_source       = "${path.module}/triggers/pre-authentication/source/"
  environment_variables = var.lambda_global_environment_variables
}

module "auction-cognito-define-challenge" {
  source                = "../../modules/default-lambda"
  description           = "Cognito DefineAuthChallenge trigger"
  function_name         = "${var.project_name}-${var.environment}-auction-define-auth"
  default-role-arn      = module.auction_cognito_lambda_role.arn
  lambda_layer          = [aws_lambda_layer_version.node_module_layer.arn, var.common_lambda_layer]
  subnet_ids            = var.lambda_subnet_ids
  security_group_ids    = [var.lambda_security_group_id]
  function_source       = "${path.module}/triggers/define-auth-challenge/source/"
  environment_variables = var.lambda_global_environment_variables
}

module "auction-cognito-create-challenge" {
  source                = "../../modules/default-lambda"
  description           = "Cognito CreateAuthChallenge trigger"
  function_name         = "${var.project_name}-${var.environment}-auction-create-auth"
  default-role-arn      = module.auction_cognito_lambda_role.arn
  lambda_layer          = [aws_lambda_layer_version.node_module_layer.arn, var.common_lambda_layer]
  subnet_ids            = var.lambda_subnet_ids
  security_group_ids    = [var.lambda_security_group_id]
  function_source       = "${path.module}/triggers/create-auth-challenge/source/"
  environment_variables = var.lambda_global_environment_variables
}

module "auction-cognito-verify-challenge" {
  source                = "../../modules/default-lambda"
  description           = "Cognito VerifyAuthChallengeResponse trigger"
  function_name         = "${var.project_name}-${var.environment}-auction-verify-auth"
  default-role-arn      = module.auction_cognito_lambda_role.arn
  lambda_layer          = [aws_lambda_layer_version.node_module_layer.arn, var.common_lambda_layer]
  subnet_ids            = var.lambda_subnet_ids
  security_group_ids    = [var.lambda_security_group_id]
  function_source       = "${path.module}/triggers/verify-auth-challenge-response/source/"
  environment_variables = var.lambda_global_environment_variables
}

module "auction-cognito-custom-message" {
  source                = "../../modules/default-lambda"
  description           = "Cognito CustomMessage trigger for forgot password"
  function_name         = "${var.project_name}-${var.environment}-auction-custom-message"
  default-role-arn      = module.auction_cognito_lambda_role.arn
  lambda_layer          = [aws_lambda_layer_version.node_module_layer.arn, var.common_lambda_layer]
  subnet_ids            = var.lambda_subnet_ids
  security_group_ids    = [var.lambda_security_group_id]
  function_source       = "${path.module}/triggers/custom-message/source/"
  environment_variables = var.lambda_global_environment_variables
}

# Allow Cognito to invoke these Lambdas
resource "aws_lambda_permission" "allow_cognito_invoke_pre" {
  statement_id  = "AllowExecutionFromCognitoPre"
  action        = "lambda:InvokeFunction"
  function_name = module.auction-cognito-pre-auth.lambda_function_arn
  principal     = "cognito-idp.amazonaws.com"
  source_arn    = aws_cognito_user_pool.auction_user_pool.arn
}

resource "aws_lambda_permission" "allow_cognito_invoke_define" {
  statement_id  = "AllowExecutionFromCognitoDefine"
  action        = "lambda:InvokeFunction"
  function_name = module.auction-cognito-define-challenge.lambda_function_arn
  principal     = "cognito-idp.amazonaws.com"
  source_arn    = aws_cognito_user_pool.auction_user_pool.arn
}

resource "aws_lambda_permission" "allow_cognito_invoke_create" {
  statement_id  = "AllowExecutionFromCognitoCreate"
  action        = "lambda:InvokeFunction"
  function_name = module.auction-cognito-create-challenge.lambda_function_arn
  principal     = "cognito-idp.amazonaws.com"
  source_arn    = aws_cognito_user_pool.auction_user_pool.arn
}

resource "aws_lambda_permission" "allow_cognito_invoke_verify" {
  statement_id  = "AllowExecutionFromCognitoVerify"
  action        = "lambda:InvokeFunction"
  function_name = module.auction-cognito-verify-challenge.lambda_function_arn
  principal     = "cognito-idp.amazonaws.com"
  source_arn    = aws_cognito_user_pool.auction_user_pool.arn
}

resource "aws_lambda_permission" "allow_cognito_invoke_custom_message" {
  statement_id  = "AllowExecutionFromCognitoCustomMessage"
  action        = "lambda:InvokeFunction"
  function_name = module.auction-cognito-custom-message.lambda_function_arn
  principal     = "cognito-idp.amazonaws.com"
  source_arn    = aws_cognito_user_pool.auction_user_pool.arn
}


# Extra inline policies for trigger role
resource "aws_iam_role_policy" "auction_cognito_trigger_ses_policy" {
  name = "${var.environment}-${var.project_name}-auction-cognito-trigger-ses-policy"
  role = module.auction_cognito_lambda_role.id

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect = "Allow",
        Action = [
          "ses:SendEmail",
          "ses:SendRawEmail"
        ],
        Resource = "*"
      }
    ]
  })
}

resource "aws_iam_role_policy" "auction_cognito_trigger_cognito_policy" {
  name = "${var.environment}-${var.project_name}-auction-cognito-trigger-cognito-policy"
  role = module.auction_cognito_lambda_role.id

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect = "Allow",
        Action = [
          "cognito-idp:AdminGetUser",
          "cognito-idp:AdminListGroupsForUser"
        ],
        Resource = aws_cognito_user_pool.auction_user_pool.arn
      }
    ]
  })
}
