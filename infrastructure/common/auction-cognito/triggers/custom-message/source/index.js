const PgPool = require(`${process.env.COMMON_LAYER_PATH}pg-pool.js`);

const pool = new PgPool();

// Helper function to get OTP email template
async function getOtpMailTemplate(tenantNo, code, language = 'ja') {
  const constants = await pool.rlsQuery(
    tenantNo,
    'SELECT * FROM "f_get_constant_by_key_string"($1,$2,$3);',
    [tenantNo, ['EMAIL_FROM', 'EMAIL_COMMON_FOOTER', 'EMAIL_FORGOT_PASSWORD_OTP'], language]
  );

  const mailFrom = constants.find(x => x.key_string === 'EMAIL_FROM');
  const footer = constants.find(x => x.key_string === 'EMAIL_COMMON_FOOTER');
  const otpTemplate = constants.find(x => x.key_string === 'EMAIL_FORGOT_PASSWORD_OTP');

  // Use localized template values or fallback to hardcoded values
  const title = otpTemplate?.value1 || 'パスワードリセット認証コード';
  const fromDisplay = mailFrom?.value2 || '';
  const fromAddress = mailFrom?.value2 || mailFrom?.value4 || '<EMAIL>';
  const sender = fromDisplay ? `"${fromDisplay}"<${fromAddress}>` : fromAddress;

  // Replace placeholders in template: {{otp_code}} = code
  const bodyTemplate = otpTemplate?.value4 || 'パスワードリセット用の認証コードは {{otp_code}} です。\n\n{{footer}}';
  let content = bodyTemplate
    .replace(/\{\{otp_code\}\}/g, code);

  if (content.includes('{{footer}}')) {
    content = content.replace(/\{\{footer\}\}/g, footer?.value4 || '');
  } else {
    content = content + '\n\n' + (footer?.value4 || '');
  }

  const bcc = otpTemplate?.value3 ? [otpTemplate.value3] : [];
  return {title, sender, content, bcc};
}

exports.handle = async (event) => {
  console.log('🔔 Custom Message Trigger Invoked!');
  console.log('📋 Trigger Source:', event.triggerSource);
  console.log('📧 Full Event:', JSON.stringify(event, null, 2));

  try {
    console.log('🎯 Checking trigger source:', event.triggerSource);

    if (event.triggerSource === 'CustomMessage_ForgotPassword') {
      console.log('✅ Forgot Password trigger detected!');

      const clientMetadata = event.request.clientMetadata || {};
      console.log('🔍 Client Metadata:', JSON.stringify(clientMetadata, null, 2));

      console.log('🚀 Sending custom SES email for forgot password');

      // Extract OTP code from the message
      const otpCode = event.request.codeParameter;
      const email = event.request.userAttributes.email;
      const tenantNo = parseInt(clientMetadata.tenantNo) || 1;
      const language = clientMetadata.language || 'ja';

      console.log('📨 Email details:', {email, otpCode, tenantNo, language});

      const {title, content} = await getOtpMailTemplate(tenantNo, otpCode, language);

      // Let Cognito send the message using our custom subject/body
      event.response.emailSubject = title;

      // Preserve line breaks: if template is plain text, convert \n to <br/>
      const hasHtmlTags = /<[^>]+>/.test(content);
      const emailBody = hasHtmlTags
        ? content
        : content.replace(/\r?\n/g, '<br/>');

      event.response.emailMessage = emailBody;
      console.log('✅ Custom OTP email content set on event.response');
      return event;
    }

    // Handle other message types for debugging
    console.log('ℹ️ Other trigger source, using default behavior:', event.triggerSource);
    return event;

  } catch (error) {
    console.error('❌ Custom Message Trigger Error:', error);
    console.error('📊 Error stack:', error.stack);
    // On error, Cognito send the default message
    return event;
  }
};
