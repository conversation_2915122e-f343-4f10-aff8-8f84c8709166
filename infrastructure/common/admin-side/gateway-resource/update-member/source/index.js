const Define = require(`${process.env.COMMON_LAYER_PATH}define`);
const Base = require(`${process.env.COMMON_LAYER_PATH}base.js`);
const PgPool = require(`${process.env.COMMON_LAYER_PATH}pg-pool.js`);
const pool = new PgPool(process.env.PGHOST);

const {
  CognitoIdentityProviderClient,
  AdminSetUserPasswordCommand,
  AdminUpdateUserAttributesCommand,
} = require('@aws-sdk/client-cognito-identity-provider');
const cognitoClient = new CognitoIdentityProviderClient({});

exports.handle = (e, ctx, cb) => {
  const params = Base.parseRequestBody(e.body);
  const tenantNo = Base.extractTenantId(e);
  const sensitiveKeys = params.sensitiveKeys || [];
  let email = '';
  console.log(`params = ${JSON.stringify(params)}`);
  console.log('update-member', params.member);
  Promise.resolve()
    .then(() => Base.startRequest(e))
    .then(() =>
      Base.checkAccessIpAddress(
        pool,
        e.requestContext.identity.sourceIp,
        Base.extractTenantId(e)
      )
    )
    .then(() => {
      const sqlParams = [tenantNo, 'member', params.member.freeField.lang];
      return pool
        .rlsQuery(tenantNo, Define.QUERY.GET_FIELD_LIST_FUNCTION, sqlParams)
        .then((result) => {
          console.log('result', result)
          const freeField = Object.assign({}, params.member.freeField);
          const errorList = []
          if (params.validation_mode === true) {
            result.forEach(field => {
              if (sensitiveKeys && sensitiveKeys.includes(field.physical_name)) {
                return;
              }
              if (field.required_flag && !freeField[field.physical_name]) {
                if (field.physical_name.includes('password')) {
                  // 物理名がパスワードの項目（パスワード・確認用パスワード・現在のパスワード）は未入力でも更新できるようにする
                  return
                }
                errorList.push(`${field.logical_name}を入力してください。`)
              }
              if (field.max_length && freeField[field.physical_name] && String(freeField[field.physical_name]).length > field.max_length) {
                errorList.push(`${field.logical_name}は${field.max_length}桁以内で入力してください。`)
              }
              if (field.max_value && freeField[field.physical_name] && Number(freeField[field.physical_name]) > field.max_value) {
                errorList.push(`${field.logical_name}は最大${field.max_value}まで入力してください。`)
              }
              if (field.max_value && freeField[field.physical_name] && Number(freeField[field.physical_name]) > field.max_value) {
                errorList.push(`${field.logical_name}は最大${field.max_value}まで入力してください。`)
              }
              if (field.regular_expressions && freeField[field.physical_name]) {
                console.log(field.regular_expressions, freeField[field.physical_name])
                const regex = new RegExp(String(field.regular_expressions))
                if (field.input_type === 'file') {
                  freeField[field.physical_name].forEach(file => {
                    console.log('file', file, regex.test(file))
                    if (!regex.test(file)) {
                      errorList.push(`${field.logical_name}の「${file}」は正規表現と一致しません。`)
                    }
                  })
                } else {
                  if (!(new RegExp(freeField[field.regular_expressions])).test(field.input_type)) {
                    errorList.push(`${field.logical_name}は正規表現と一致しません。`)
                  }
                }
              }
            });
            if (errorList.length > 0) return Promise.reject({
              status: 400,
              errors: errorList,
            })
            return Promise.reject({
              status: 200,
              message: '',
            })
          }
          return Promise.resolve()
        })
    })
    .then(() => {
      console.log('GET ORIGINAL MEMBER INFO');
      return pool
        .rlsQuery(Base.extractTenantId(e), Define.QUERY.GET_MEMBER_FULL_FUNCTION, [
          Base.extractTenantId(e),
          params.member.memberRequestNo,
        ])
        .then(members => {
          console.log(`members: ${JSON.stringify(members)}`);
          if (members && members.length > 0) {
            email = members[0]?.free_field?.email;
            return Promise.resolve();
          }
          return Promise.reject({
            status: 400,
            message: Define.MESSAGE.E000138,
          });
        });
    })
    .then(() => {
      console.log('UPDATE MEMBER');
      const member = params.member;
      const bidLimitAmount = member.bidLimitAmount === '' ? null : member.bidLimitAmount;

      // パスワードが入力されているかチェック
      const hasPasswordUpdate = member.freeField?.password && member.freeField.password.trim() !== '';

      // パスワードフィールドを除外したfree_fieldを作成
      const filteredFreeField = {};
      Object.keys(member.freeField || {}).forEach(key => {
        if (!key.includes('password')) {
          filteredFreeField[key] = member.freeField[key];
        }
      });
      if (member.memberNo) {
        if (hasPasswordUpdate) {
          // パスワードが入力されている場合、Cognitoのパスワード更新後にDB更新
          if (!email) {
            return Promise.reject({
              status: 400,
              errors: {
                email: Define.MESSAGE.E100048,
              },
            });
          }

          const setPasswordCommand = new AdminSetUserPasswordCommand({
            UserPoolId: process.env.AUCTION_USER_POOL_ID_IN_ADMIN,
            Username: email,
            Password: member.freeField.password,
            Permanent: true
          });

          return cognitoClient.send(setPasswordCommand)
            .then(() => {
              return pool.rlsQuery(Base.extractTenantId(e), Define.QUERY.UPDATE_MEMBER_EDIT_FUNCTION, [
                member.memberNo,
                member.bidAllowFlag,
                member.emailDeliveryFlag,
                filteredFreeField,
                Base.extractAdminNo(e),
                bidLimitAmount, // 会員固有の入札上限額
              ]);
            })
            .catch(error => {
              console.log('Cognito password update error:', error);
              return Promise.reject({
                status: 400,
                errors: {
                  password: Define.MESSAGE.E100049,
                },
              });
            });
        } else {
          console.log({
            'memberNo': member.memberNo,
            'bidAllowFlag': member.bidAllowFlag,
            'emailDeliveryFlag': member.emailDeliveryFlag,
            'adminNo': Base.extractAdminNo(e),
            'bidLimitAmount': bidLimitAmount, // 会員固有の入札上限額
          })
          // 会員情報更新
          return pool.rlsQuery(Base.extractTenantId(e), Define.QUERY.UPDATE_MEMBER_EDIT_FUNCTION, [
            member.memberNo,
            member.bidAllowFlag,
            member.emailDeliveryFlag,
            filteredFreeField,
            Base.extractAdminNo(e),
            bidLimitAmount, // 会員固有の入札上限額
          ]);
        }
      } else {
        // 会員申請情報更新
        return pool.rlsQuery(Base.extractTenantId(e), Define.QUERY.UPDATE_MEMBER_REQUEST_FUNCTION, [
          Base.extractTenantId(e),
          member.memberRequestNo,
          99,
          Object.assign({}, filteredFreeField, {member_bid_limit: member.bidLimitAmount}),
          Base.extractAdminNo(e),
        ]);
      }
    })
    .then(result => {
      console.log(`result = ${JSON.stringify(result)}`);

      // Check if member name was updated and update Cognito custom:member_name
      const member = params.member;
      const memberNameChanged = member.freeField?.memberName && email;

      if (memberNameChanged) {
        console.log('Updating Cognito custom:member_name to:', member.freeField.memberName);

        const updateAttributesCommand = new AdminUpdateUserAttributesCommand({
          UserPoolId: process.env.AUCTION_USER_POOL_ID_IN_ADMIN,
          Username: email,
          UserAttributes: [
            {
              Name: 'custom:member_name',
              Value: member.freeField.memberName
            }
          ]
        });

        return cognitoClient.send(updateAttributesCommand)
          .then(() => {
            console.log('Successfully updated Cognito custom:member_name');
            return Base.createSuccessResponse(cb, result);
          })
          .catch(error => {
            console.error('Failed to update Cognito custom:member_name:', error);
            return Base.createSuccessResponse(cb, result);
          });
      }

      return Base.createSuccessResponse(cb, result);
    })
    .catch(error => Base.createErrorResponse(cb, error));
};
