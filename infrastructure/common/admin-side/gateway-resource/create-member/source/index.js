const Define = require(`${process.env.COMMON_LAYER_PATH}define`);
const Validator = require(`${process.env.COMMON_LAYER_PATH}validator.js`);
const Base = require(`${process.env.COMMON_LAYER_PATH}base.js`);
const Common = require(`${process.env.COMMON_LAYER_PATH}common.js`);
const PgPool = require(`${process.env.COMMON_LAYER_PATH}pg-pool.js`);
const jwt = require('jsonwebtoken');
const {
  AdminAddUserToGroupCommand,
  AdminCreateUserCommand,
  AdminSetUserPasswordCommand,
  AdminDisableUserCommand,
  CognitoIdentityProviderClient,
} = require('@aws-sdk/client-cognito-identity-provider');

const pool = new PgPool(process.env.PGHOST);
const cognitoClient = new CognitoIdentityProviderClient({});

// JWT configuration for token generation
const JWT_KEY = process.env.JWT_KEY;

async function rollbackMemberCreation(memberNo, tenantNo, poolInstance, memberRequestNo = null) {
  if (!memberNo || !tenantNo) {
    console.warn('🔴 rollbackMemberCreation: Missing required parameters', {memberNo, tenantNo});
    return Promise.reject(new Error('Missing memberNo or tenantNo for rollback'));
  }

  try {
    console.log('🧹 Starting member rollback process for member_no:', memberNo, 'tenant_no:', tenantNo);

    let actualMemberRequestNo = memberRequestNo;

    // 1: Get member_request_no if not provided
    if (!actualMemberRequestNo) {
      const checkMemberSql = 'SELECT member_no, member_request_no FROM m_member WHERE member_no = $1 AND tenant_no = $2 AND delete_flag = 0';
      const memberExists = await poolInstance.rlsQuery(tenantNo, checkMemberSql, [memberNo, tenantNo]);

      if (memberExists.length === 0) {
        console.log('ℹ️ No member found to rollback for member_no:', memberNo);
        return Promise.resolve();
      }

      actualMemberRequestNo = memberExists[0].member_request_no;
    }

    console.log('📋 Found member to rollback:', {memberNo, memberRequestNo: actualMemberRequestNo, tenantNo});

    // 2: Delete from t_member_status_history
    const deleteHistorySql = 'DELETE FROM t_member_status_history WHERE tenant_no = $1 AND member_request_no = $2';
    await poolInstance.rlsQuery(tenantNo, deleteHistorySql, [tenantNo, actualMemberRequestNo]);
    console.log('✅ Deleted member status history for member_request_no:', actualMemberRequestNo);

    // 3: Delete from m_user
    const deleteUserSql = 'DELETE FROM m_user WHERE tenant_no = $1 AND member_no = $2';
    await poolInstance.rlsQuery(tenantNo, deleteUserSql, [tenantNo, memberNo]);
    console.log('✅ Deleted user record for member_no:', memberNo);

    // 4: Delete from m_member
    const deleteMemberSql = 'DELETE FROM m_member WHERE tenant_no = $1 AND member_no = $2';
    await poolInstance.rlsQuery(tenantNo, deleteMemberSql, [tenantNo, memberNo]);
    console.log('✅ Deleted member record for member_no:', memberNo);

    // 5: Clean up t_member_request if it exists (it has delete_flag=1 already from f_create_member)
    const deleteRequestSql = 'DELETE FROM t_member_request WHERE tenant_no = $1 AND member_request_no = $2';
    await poolInstance.rlsQuery(tenantNo, deleteRequestSql, [tenantNo, actualMemberRequestNo]);
    console.log('✅ Deleted member request for member_request_no:', actualMemberRequestNo);

    console.log('🎉 Member rollback completed successfully for member_no:', memberNo);
    return Promise.resolve();

  } catch (rollbackError) {
    console.error('💥 Error during member rollback:', {
      error: rollbackError,
      memberNo,
      tenantNo,
      stack: rollbackError.stack
    });
    return Promise.reject(rollbackError);
  }
}

exports.handle = (e, ctx, cb) => {
  const params = Base.parseRequestBody(e.body);
  const tenantNo = Base.extractTenantId(e);
  console.log(`params = ${JSON.stringify(params)}`);
  console.log('create-member');
  Promise.resolve()
    .then(() => Base.startRequest(e))
    .then(() =>
      Base.checkAccessIpAddress(
        pool,
        e.requestContext.identity.sourceIp,
        tenantNo
      )
    )
    .then(() => {
      const sqlParams = [tenantNo, 'member', params.member.freeField.lang];
      return pool
        .rlsQuery(tenantNo, Define.QUERY.GET_FIELD_LIST_FUNCTION, sqlParams)
        .then((result) => {
          console.log('result of GET_FIELD_LIST_FUNCTION:', result)
          const freeField = Object.assign({}, params.member.freeField);
          const errorList = []
          if (params.validation_mode === true) {
            const email = freeField.email;
            return pool.rlsQuery(tenantNo, 'SELECT * FROM f_get_member_by_email($1, $2)', [tenantNo, email])
              .then(existingMember => {
                console.log('💬 log of existingMember : ', existingMember)
                if (existingMember && existingMember.length > 0) {
                  errorList.push('このメールアドレスは既に登録されています。');
                }

                // Continue with field validation
                result.forEach(field => {
                  // 現在のパスワードは入力対象外
                  if (field.required_flag && !freeField[field.physical_name] && field.physical_name !== 'current_password') {
                    errorList.push(`${field.logical_name}を入力してください。`)
                  }
                  if (field.max_length && freeField[field.physical_name] && String(freeField[field.physical_name]).length > field.max_length) {
                    errorList.push(`${field.logical_name}は${field.max_length}桁以内で入力してください。`)
                  }
                  if (field.max_value && freeField[field.physical_name] && Number(freeField[field.physical_name]) > field.max_value) {
                    errorList.push(`${field.logical_name}は最大${field.max_value}まで入力してください。`)
                  }
                  if (field.max_value && freeField[field.physical_name] && Number(freeField[field.physical_name]) > field.max_value) {
                    errorList.push(`${field.logical_name}は最大${field.max_value}まで入力してください。`)
                  }
                  if (field.regular_expressions && freeField[field.physical_name]) {
                    console.log(field.regular_expressions, freeField[field.physical_name])
                    const regex = new RegExp(String(field.regular_expressions))
                    if (field.input_type === 'file') {
                      freeField[field.physical_name].forEach(file => {
                        console.log('file', file, regex.test(file))
                        if (!regex.test(file)) {
                          errorList.push(`${field.logical_name}の「${file}」は正規表現と一致しません。`)
                        }
                      })
                    } else {
                      if (!(new RegExp(freeField[field.regular_expressions])).test(field.input_type)) {
                        errorList.push(`${field.logical_name}は正規表現と一致しません。`)
                      }
                    }
                  }
                });

                if (errorList.length > 0) return Promise.reject({
                  status: 400,
                  errors: errorList,
                })
                return Promise.reject({
                  status: 200,
                  message: '',
                })
              })
              .catch(emailCheckError => {
                console.log('🐞 log of email check existed : ', emailCheckError)
                if (emailCheckError.status === 400) {
                  console.log('Validation error found:', emailCheckError);
                  return Promise.reject(emailCheckError);
                }

                if (emailCheckError.status === 200) {
                  return Promise.reject(emailCheckError);
                }

                console.error('Database error checking email existence:', emailCheckError);
                return Promise.reject({
                  status: 500,
                  message: 'データベースエラーが発生しました。'
                });
              });
          }
          return Promise.resolve()
        })
    })
    .then(() => {
      console.log('CREATE MEMBER');
      const randomPassword = Common.randomString(10);
      console.log('randomPassword', randomPassword);
      return Base.hashPassword(randomPassword)
        .then(hashPassword => {
          console.log('hashPassword', hashPassword);
          const freeField = Object.assign({}, params.member.freeField);
          // 3.1.1 Keep logic for create user in database same as currently flow(f_create_member)
          return pool.rlsQuery(tenantNo, Define.QUERY.CREATE_MEMBER_FUNCTION, [
            tenantNo,
            freeField,
            params.member.bidAllowFlag,
            params.member.emailDeliveryFlag,
            1, // require password change
            0, // require confirm token
            null, // token string
            null, // token expire
            Base.extractAdminNo(e),
          ]);
        })
        .then(dbResult => {
          console.log('👷‍♂️ log of dbResult(after create member in db) : ', dbResult)

          // 3.1.2 Add cognito create user logic
          const email = params.member.freeField.email;
          const memberName = params.member.freeField.memberName || email;

          console.log('log of tenantNo : ', tenantNo)
          console.log('Creating Cognito user for:', email);

          // Create temporary password for Cognito (will be replaced by user)
          const tempCognitoPassword = Common.randomString(12) + 'A1!';

          return Promise.resolve()
            .then(() => {
              // Create user in Cognito
              const createUserCommand = new AdminCreateUserCommand({
                UserPoolId: process.env.AUCTION_COGNITO_USER_POOL_ID,
                Username: email,
                UserAttributes: [
                  {Name: 'email', Value: email},
                  {Name: 'email_verified', Value: 'true'},
                  {Name: 'custom:member_no', Value: String(dbResult[0]?.export_member_no || '')},
                  {Name: 'custom:user_no', Value: String(dbResult[0]?.export_user_no || '')},
                  {Name: 'custom:member_name', Value: dbResult[0]?.export_member_name || memberName},
                ],
                TemporaryPassword: tempCognitoPassword,
                MessageAction: 'SUPPRESS', // Suppress default welcome email
              });

              return cognitoClient.send(createUserCommand);
            })
            .then(() => {
              // Set permanent password
              const setPasswordCommand = new AdminSetUserPasswordCommand({
                UserPoolId: process.env.AUCTION_COGNITO_USER_POOL_ID,
                Username: email,
                Password: tempCognitoPassword,
                Permanent: true,
              });

              return cognitoClient.send(setPasswordCommand);
            })
            .then(() => {
              const groupName = `tenant-id:${tenantNo}`;
              // Add user to appropriate group
              const addToGroupCommand = new AdminAddUserToGroupCommand({
                UserPoolId: process.env.AUCTION_COGNITO_USER_POOL_ID,
                Username: email,
                GroupName: groupName,
              });
              return cognitoClient.send(addToGroupCommand).then(() => {
                console.log(`✓ User added to tenant group: ${groupName}`)
                return Promise.resolve()
              })
            })
            .then(() => {
              // Disable user until password is set (provisional state)
              const disableUserCommand = new AdminDisableUserCommand({
                UserPoolId: process.env.AUCTION_COGNITO_USER_POOL_ID,
                Username: email,
              });

              return cognitoClient.send(disableUserCommand);
            })
            .then(() => {
              console.log('Cognito user created and disabled successfully');
              return dbResult; // Return original database result
            })
            .catch(cognitoError => {
              console.error('🚨 Cognito user creation failed:', {
                error: cognitoError,
                errorName: cognitoError.name,
                errorMessage: cognitoError.message,
                memberNo: dbResult[0]?.export_member_no,
                email: email,
                tenantNo: tenantNo
              });

              // Rollback database member creation since Cognito failed
              const memberNo = dbResult[0]?.export_member_no;
              const memberRequestNo = null;
              if (memberNo) {
                console.log('🔄 Starting database rollback for member_no:', memberNo, 'member_request_no:', memberRequestNo);

                // Handle rollback with explicit success/failure paths
                return rollbackMemberCreation(memberNo, tenantNo, pool, memberRequestNo)
                  .then(() => {
                    console.log('✅ Database rollback completed successfully');
                    // After successful rollback, throw the original Cognito error
                    throw {
                      status: 500,
                      name: 'CognitoMemberCreationError',
                      message: 'Member creation failed due to Cognito error. Database changes have been rolled back.',
                      originalError: cognitoError
                    };
                  })
                  .catch(rollbackError => {
                    // Check if this is our intentional error throw from successful rollback
                    if (rollbackError.name === 'CognitoMemberCreationError') {
                      // This is the intended error after successful rollback, re-throw it
                      throw rollbackError;
                    }

                    // This is an actual rollback failure
                    console.error('❌ Database rollback failed:', rollbackError);
                    throw {
                      status: 500,
                      name: 'CriticalMemberCreationError',
                      message: 'Member creation failed due to Cognito error and database rollback also failed. Manual cleanup may be required.',
                      cognitoError: cognitoError,
                      rollbackError: rollbackError,
                      memberNo: memberNo
                    };
                  });
              } else {
                console.warn('⚠️ No member_no found in dbResult, skipping rollback');
                return Promise.reject({
                  status: 500,
                  name: 'CognitoMemberCreationError',
                  message: 'Member creation failed due to Cognito error',
                  originalError: cognitoError
                });
              }
            });
        })
        .then(dbResult => {
          console.log('🧨 log of create member in db: ', dbResult)
          // 3.2.2 Send email with link to first-password account activation
          const language = params.member.freeField.lang || 'ja';
          const email = params.member.freeField.email;
          const tenantNo = Base.extractTenantId(e);

          return pool.rlsQuery(tenantNo,
            'SELECT * FROM "f_get_constants_by_keys"($1,$2,$3);',
            [
              [
                'ADMIN_CREATE_MEMBER_PASSWORD_RESET',
                'EMAIL_COMMON_FOOTER',
                'EMAIL_FROM',
              ],
              tenantNo,
              language,
            ]
          ).then(constants => {
            console.log('📋 Retrieved constants:', constants);
            const mailFrom = constants.find(x => x.key_string === 'EMAIL_FROM');
            const footer = constants.find(x => x.key_string === 'EMAIL_COMMON_FOOTER') || {};
            const mail = constants.find(x => x.key_string === 'ADMIN_CREATE_MEMBER_PASSWORD_RESET') || {};
            console.log("%c 🏂: exports.handle -> mail ", "font-size:16px;background-color:#654f3a;color:white;", mail)
            console.log('📬 mailFrom constant:', mailFrom);

            if (!mailFrom) {
              console.error('EMAIL_FROM constant not found');
              throw {
                status: 500,
                name: 'EmailConfigError',
                message: 'EMAIL_FROM constant is not configured'
              };
            }

            if (!mail || !mail.value1) {
              console.error('ADMIN_CREATE_MEMBER_PASSWORD_RESET constant not found or missing value1');
              throw {
                status: 500,
                name: 'EmailConfigError',
                message: 'Email template constant is not properly configured'
              };
            }

            const title = mail.value1;
            const fromAddress = mailFrom.value2 || mailFrom.value4;

            if (!fromAddress) {
              console.error('fromAddress is null or empty in email constant');
              throw {
                status: 500,
                name: 'EmailConfigError',
                message: 'From address is not configured in email constant'
              };
            }

            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(fromAddress)) {
              console.error('Invalid fromAddress format:', fromAddress);
              throw {
                status: 500,
                name: 'EmailConfigError',
                message: 'From address has invalid email format'
              };
            }

            if (!email || !emailRegex.test(email)) {
              console.error('Invalid receiver email format:', email);
              throw {
                status: 500,
                name: 'EmailConfigError',
                message: 'Receiver email has invalid format'
              };
            }

            const receivers = [email];
            const BASE_URL = mail.value2;

            if (!BASE_URL) {
              console.error('BASE_URL is null or empty in email constant');
              throw {
                status: 500,
                name: 'EmailConfigError',
                message: 'BASE_URL is not configured in email template constant'
              };
            }

            try {
              new URL(BASE_URL);
            } catch (urlError) {
              console.error('Invalid BASE_URL format:', BASE_URL);
              throw {
                status: 500,
                name: 'EmailConfigError',
                message: 'BASE_URL has invalid format in email template constant'
              };
            }

            const bcc = mail.value3 ? mail.value3.split(',') : [];

            if (!JWT_KEY) {
              console.error('JWT_KEY is not configured');
              throw {
                status: 500,
                name: 'ConfigurationError',
                message: 'JWT_KEY environment variable is not configured'
              };
            }

            const tokenPayload = {
              email: email,
              issuedAt: new Date().toISOString(),
              type: 'init-password'
            };
            const token = jwt.sign(tokenPayload, JWT_KEY, {expiresIn: '24h'});

            const initPasswordUrl = `${BASE_URL}/init-password?token=${token}`;

            const content = Common.format(mail.value4, [
              initPasswordUrl,
              footer.value4 || '',
            ]);
            console.log('☎️ log of content for sendMailBySES : ', content)
            console.log('📧 Email parameters - title:', title, 'fromAddress:', fromAddress, 'receivers:', receivers, 'bcc:', bcc);

            return Common.sendMailBySES(
              title,
              content,
              fromAddress,
              receivers,
              bcc
            ).then(() => {
              console.log('Initial password setup email sent to:', email);
              return dbResult;
            }).catch(emailError => {
              console.error('Failed to send initial password email:', emailError);
              throw {
                status: 500,
                name: 'EmailSendError',
                message: 'Failed to send initial password email to user',
                originalError: emailError
              };
            });
          });
        });
    })
    .then(result => {
      console.log(`result = ${JSON.stringify(result)}`);
      return Base.createSuccessResponse(cb, result);
    })
    .catch(error => Base.createErrorResponse(cb, error));
};
