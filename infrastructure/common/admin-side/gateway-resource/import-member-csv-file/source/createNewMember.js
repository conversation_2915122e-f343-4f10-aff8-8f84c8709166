const Define = require(`${process.env.COMMON_LAYER_PATH}define`);
const Common = require(`${process.env.COMMON_LAYER_PATH}common.js`);
const Base = require(`${process.env.COMMON_LAYER_PATH}base.js`);

const createNewMember = (pool, member, filteredFreeField, e) => {
  return Promise.resolve().then(() => {
    console.log('CREATE MEMBER');
    const randomPassword = Common.randomString(10);
    console.log('randomPassword', randomPassword);
    const tenant_no = Base.extractTenantId(e);
    const admin_no = Base.extractAdminNo(e);

    return Base.hashPassword(randomPassword)
      .then(hashPassword => {
        const freeField = Object.assign({}, filteredFreeField);
        return pool.rlsQuery(Base.extractTenantId(e), Define.QUERY.CREATE_MEMBER_FUNCTION, [
          tenant_no,
          freeField,
          member.bid_allow_flag,
          member.email_delivery_flag,
          1, // require password change
          0, // require confirm token
          null, // token string
          null, // token expire
          admin_no,
        ]);
      })
      .then(createdMember => {
        // Send registered information to user's email
        const language =
          filteredFreeField.emailLang ||
          filteredFreeField.lang ||
          (filteredFreeField.country === 'JP' ? 'ja' : 'en');
        return pool.rlsQuery(Base.extractTenantId(e),
          'SELECT * FROM "f_get_constant_by_key_string"($1,$2,$3);',
          [
            tenant_no,
            [
              'EMAIL_TEMPORARY_PASSWORD_INFO_FOR_MEMBER',
              'EMAIL_COMMON_FOOTER',
              'EMAIL_FROM',
            ],
            language,
          ]
        );
      })
      .then(constants => {
        const mailFrom =
          constants.find(content => content.key_string === 'EMAIL_FROM')
            ?.value2 || null;
        const footer =
          constants.find(x => x.key_string === 'EMAIL_COMMON_FOOTER') || {};
        return Promise.all([
          Promise.resolve().then(() => {
            const mail =
              constants.find(
                x => x.key_string === 'EMAIL_TEMPORARY_PASSWORD_INFO_FOR_MEMBER'
              ) || {};
            const title = mail.value1;
            const sender = mailFrom
              ? `"${mailFrom}"<${mail.value2}>`
              : mail.value2;
            const receivers = [filteredFreeField.email];
            const bcc = mail.value3 ? mail.value3.split(',') : [];
            const content = Common.format(mail.value4, [
              randomPassword,
              footer.value4,
            ]);

            return Common.sendMailBySES(title, content, sender, receivers, bcc);
          }),
        ]);
      });
  });
};

module.exports = createNewMember;
