resource "aws_iam_role_policy" "ses_send_mail_policy" {
  name = "${var.environment}-${var.project_name}-${basename(path.module)}-ses-send-mail-policy"
  role = module.gateway-resource.lambda_iam_role_id

  policy = data.aws_iam_policy_document.ses_send_mail_policy.json
}

resource "aws_iam_role_policy" "cognito_policy" {
  name = "${var.project_name}-${var.environment}-${basename(path.module)}-cognito-policy"
  role = module.gateway-resource.lambda_iam_role_id

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Action = [
          "cognito-idp:ChangePassword",
          "cognito-idp:AdminUpdateUserAttributes",
        ],
        Effect   = "Allow",
        Resource = "arn:aws:cognito-idp:${data.aws_region.current.id}:${data.aws_caller_identity.current.account_id}:userpool/${var.cognito_user_pool_id}"
      }
    ]
  })
}

data "aws_region" "current" {}
data "aws_caller_identity" "current" {}
