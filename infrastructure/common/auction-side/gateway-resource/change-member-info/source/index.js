const Validator = require(`${process.env.COMMON_LAYER_PATH}validator.js`);
const Base = require(`${process.env.COMMON_LAYER_PATH}base.js`);
const PgPool = require(`${process.env.COMMON_LAYER_PATH}pg-pool.js`);
const Common = require(`${process.env.COMMON_LAYER_PATH}common.js`);
const pool = new PgPool();

const {
  CognitoIdentityProviderClient,
  ChangePasswordCommand,
  AdminUpdateUserAttributesCommand,
} = require('@aws-sdk/client-cognito-identity-provider');
const cognitoClient = new CognitoIdentityProviderClient({});

exports.handle = (e, ctx, cb) => {
  const params = Common.parseRequestBody(e.body)
  const header = e.headers;
  const authorizer = {
    user_no: Common.extractUserNo(e),
    member_name: Common.extractMemberName(e),
    member_no: Common.extractMemberNo(e),
    tenant_no: Common.extractTenantId(e),
  }
  const base = new Base(pool, params.languageCode);
  let tenant = null;
  const memberData = params.registerData || {};
  const filteredFreeField = {};
  const validateFlag = params.validateFlag || false;

  Promise.resolve()
    .then(() => base.startRequest(e))
    .then(() => {
      return base
        .checkOrigin(header.origin || header.Origin || e.context?.access_ip)
        .then(data => {
          tenant = data;
        });
    })
    .then(() => {
      const sqlParams = [authorizer.tenant_no, 'member', base.language,];
      const sql = `SELECT * FROM "f_get_field_item_by_auction"(${Common.sqlParamNumber(sqlParams.length)});`
      return pool.rlsQuery(authorizer.tenant_no, sql, sqlParams)
    })
    .then((result) => {
      // Validate
      const freeField = Object.assign({}, memberData);
      const errorList = {};
      result.forEach(field => {
        if (field.required_flag && !freeField[field.physical_name]) {
          if (field.physical_name.includes('password')) {
            // パスワード・確認用パスワード・現在のパスワードは未入力でも更新できるようにする
            return
          }
          errorList[field.physical_name] = Common.format(base.define.message.E000027, [field.logical_name])
        }
        if (field.max_length && freeField[field.physical_name] && String(freeField[field.physical_name]).length > field.max_length) {
          errorList[field.physical_name] = Common.format(base.define.message.E000028, [field.logical_name, field.max_length])
        }
        if (field.max_value && freeField[field.physical_name] && Number(freeField[field.physical_name]) > field.max_value) {
          errorList[field.physical_name] = Common.format(base.define.message.E000029, [field.logical_name, field.max_value])
        }
        if (field.regular_expressions && freeField[field.physical_name]) {
          console.log(field.regular_expressions, freeField[field.physical_name])
          const regex = new RegExp(String(field.regular_expressions))
          if (field.input_type === 'file') {
            freeField[field.physical_name].forEach(file => {
              console.log('file', file, regex.test(file))
              if (!regex.test(file)) {
                errorList[field.physical_name] = Common.format(base.define.message.E000031, [field.logical_name, file])
              }
            })
          } else {
            if (!(new RegExp(String(field.regular_expressions))).test(String(freeField[field.physical_name]))) {
              errorList[field.physical_name] = Common.format(base.define.message.E000030, [field.logical_name])
            }
          }
        }
      });
      if (Object.keys(errorList).length > 0) return Promise.reject({
        status: 400,
        errors: errorList,
      })

      if (validateFlag === true) {
        return Promise.reject({
          status: 200,
          message: '',
        })
      }
      return Promise.resolve()
    })
    .then(() => {
      // パスワードが入力されているかチェック
      const hasPasswordUpdate = memberData?.password && memberData.password.trim() !== '';

      // パスワードフィールドを除外したfree_fieldを作成
      Object.keys(memberData || {}).forEach(key => {
        if (!key.includes('password')) {
          filteredFreeField[key] = memberData[key];
        }
      });

      // パスワード更新時のみCognitoのパスワード更新を実行
      if (hasPasswordUpdate) {
        const changePasswordCommand = new ChangePasswordCommand({
          PreviousPassword: memberData.current_password,
          ProposedPassword: memberData.password,
          AccessToken: params.accessToken,
        });

        return cognitoClient.send(changePasswordCommand)
          .catch(error => {
            console.log('Cognito password update error:', error);
            return Promise.reject({
              status: 400,
              errors: {
                password: base.define.message.E300010,
              },
            });
          });
      }
      return Promise.resolve();
    })
    .then(() => {
      // メンバー情報更新処理
      const sqlParams = [authorizer.tenant_no, authorizer.member_no, filteredFreeField, null];
      return pool.query(
        'SELECT * FROM "f_update_member_info"($1,$2,$3,$4);',
        sqlParams
      );
    })
    .then(result => {
      console.log('result: ', result);
      const member = result?.length > 0 ? result[0] : null;
      if (!member) {
        return Promise.reject({
          status: 400,
          errors: {
            common: base.define.message.E900001,
          },
        });
      }

      // Update Cognito custom:member_name if memberName was changed
      const memberNameChanged = filteredFreeField.memberName &&
        filteredFreeField.memberName !== authorizer.member_name;

      if (memberNameChanged) {
        console.log('Updating Cognito custom:member_name from', authorizer.member_name, 'to', filteredFreeField.memberName);

        // Get member email for Cognito user identification
        const memberEmail = member.free_field?.email;
        if (!memberEmail) {
          console.warn('No email found for member, skipping Cognito update');
          return Promise.resolve(member);
        } else {
          const updateAttributesCommand = new AdminUpdateUserAttributesCommand({
            UserPoolId: process.env.AUCTION_COGNITO_USER_POOL_ID,
            Username: memberEmail,
            UserAttributes: [
              {
                Name: 'custom:member_name',
                Value: filteredFreeField.memberName
              }
            ]
          });

          return cognitoClient.send(updateAttributesCommand)
            .then(() => {
              console.log('Successfully updated Cognito custom:member_name');
              return Promise.resolve(member);
            })
            .catch(error => {
              console.error('Failed to update Cognito custom:member_name:', error);
              return Promise.resolve(member);
            });
        }
      }

      return Promise.resolve(member);
    })
    .then(member => {
      return pool.query(
        'SELECT * FROM "f_get_constant_by_key_string"($1,$2,$3);',
        [
          tenant.tenant_no,
          [
            'EMAIL_CHANGE_MEMBER_INFO_FOR_MEMBER',
            'EMAIL_COMMON_FOOTER',
            'EMAIL_FROM',
          ],
          base.language,
        ]
      )
        .then(constants => {
          const footer =
            constants.find(x => x.key_string === 'EMAIL_COMMON_FOOTER') ||
            {};
          const mailFrom =
            constants.find(x => x.key_string === 'EMAIL_FROM')?.value2 ||
            null;
          const mail =
            constants.find(
              x => x.key_string === 'EMAIL_CHANGE_MEMBER_INFO_FOR_MEMBER'
            ) || {};
          const title = mail.value1;
          const sender = mailFrom
            ? `"${mailFrom}"<${mail.value2}>`
            : mail.value2;
          const receivers = member?.free_field.email ? [member?.free_field.email] : [];
          const bcc = mail.value3 ? mail.value3.split(',') : [];
          const content = Common.format(mail.value4, [
            footer.value4 || '',
          ]);

          // 受信者がいない場合はメール送信をスキップ
          if (receivers.length === 0) {
            console.log('No receivers found, skipping email send');
            return Promise.resolve({message: 'Email skipped - no receivers'});
          }

          return Common.sendMailBySES(
            title,
            content,
            sender,
            receivers,
            bcc
          );
        });
    })
    .then(data => {
      return base.createSuccessResponse(cb, data);
    })
    .catch(error => base.createErrorResponse(cb, error));
};
