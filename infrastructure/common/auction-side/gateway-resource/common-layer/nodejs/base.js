const Define = require('./define')
const {InvokeCommand, LambdaClient, LogType} = require('@aws-sdk/client-lambda')
const {
  GetObjectCommand,
  PutObjectCommand,
  S3Client,
} = require('@aws-sdk/client-s3')
const {getSignedUrl} = require('@aws-sdk/s3-request-presigner')
const iconv = require('iconv-lite')
const {parseAsync} = require('json2csv')
const request = require('request-promise')
const Jwt = require('jsonwebtoken')
const Bcrypt = require('bcryptjs')
const {Netmask} = require('netmask')
const util = require('node:util')
const zlib = require('node:zlib')
const jwt = require('jsonwebtoken')
const Common = require('./common.js')

class Base {
  constructor(pool, language) {
    this.pool = pool
    this.language = Object.keys(Define.messages()).includes(language)
      ? language
      : 'en'
    this.define = new Define(this.language)
  }

  /**
   * ユーザIDとパスワードを確認する
   * @param {object} pool DB情報
   * @param {string} userId ユーザID
   * @param {string} password パスワード
   * @param tenantNo
   * @returns {Promise} コールバック関数
   */
  passwordVerify(password, userId, tenantNo) {
    return new Promise((resolve, reject) => {
      let user = null
      Promise.resolve()
        .then(() =>
          this.pool.query('SELECT * FROM "f_get_user_for_login"($1,$2);', [
            userId,
            tenantNo,
          ])
        )
        .then((result) => {
          console.log('result', result)
          if (result.length === 0) {
            const response = {
              status: 401,
              message: this.define.message.E000004,
            }
            return reject(response)
          }
          user = result[0]

          return Bcrypt.compare(password, user.password)
        })
        .then((result) => {
          if (result) {
            return resolve(user)
          }
          const response = {
            status: 401,
            message: this.define.message.E000004,
          }

          return reject(response)
        })
        .catch((error) => {
          return reject(error)
        })
    })
  }

  /**
   * 入力値とBCryptでハッシュ化した値と比較する
   * @param {string} password ハッシュ化するパスワード
   * @returns {Promise} コールバック関数
   */
  hashPassword(password) {
    return new Promise((resolve, reject) => {
      try {
        const saltRounds = 10
        // ソルト生成
        const salt = Bcrypt.genSaltSync(saltRounds)
        // ハッシュ化
        const hash = Bcrypt.hashSync(password, salt)
        return resolve(hash)
      } catch (error) {
        return reject(error)
      }
    })
  }

  /**
   * IP制限確認
   * @param {string} accessIp IPアドレス
   * @returns {Promise} コールバック関数
   */
  checkAccessIpAddress(accessIp) {
    return new Promise((resolve, reject) => {
      if (
        Define.ACCESS_IP_ADDRESS_LIST.map((x) => new Netmask(x)).find((mask) =>
          mask.contains(accessIp)
        )
      ) {
        return resolve()
      }
      return reject({
        status: 401,
        message: this.define.message.E800002,
      })
    })
  }

  /**
   * レスポンス作成
   * @param {object} cb コールバック関数
   * @param {object} response レスポンス
   * @param data
   * @param {boolean} compress 圧縮する
   * @returns {object} コールバック関数を呼ぶ
   *
   * @example
   * // Example 1: Basic success response without messageKey
   * this.createSuccessResponse(callback, {
   *   result: 'success',
   *   user_id: 'USER123'
   * })
   * // Output: { result: 'success', user_id: 'USER123' }
   *
   * @example
   * // Example 2: Success response with messageKey (Auto localized)
   * this.createSuccessResponse(callback, {
   *   messageKey: 'S000001',
   *   member_id: 'M0001',
   *   status: 'confirmed'
   * })
   * // If language is 'ja': { messageKey: 'S000001', member_id: 'M0001', status: 'confirmed', message: '会員登録が正常に確認されました。' }
   * // If language is 'en': { messageKey: 'S000001', member_id: 'M0001', status: 'confirmed', message: 'Member registration confirmed successfully.' }
   */
  createSuccessResponse(cb, data, compress = true) {
    const deflate = util.promisify(zlib.deflate)

    // If data contains messageKey and corresponding message exists in define.message,
    let processedData = data
    if (data && data.messageKey && this.define.message[data.messageKey]) {
      processedData = {
        ...data,
        message: this.define.message[data.messageKey],
      }
    }

    return Promise.resolve()
      .then(() => {
        if (compress) {
          return deflate(JSON.stringify(processedData))
        }
        return JSON.stringify(processedData)
      })
      .then((data) => this.checkResponseSize(data))
      .then((processedData) => {
        // Return proper API Gateway response format
        const response = {
          statusCode: 200,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Credentials': true,
            'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
            'Access-Control-Allow-Headers':
              'Content-Type, Authorization, X-Requested-With',
          },
          body:
            typeof processedData === 'string'
              ? processedData
              : JSON.stringify(processedData),
          isBase64Encoded: false,
        }
        return cb(null, response)
      })
      .catch((error) => this.createErrorResponse(cb, error))
  }

  /**
   * Errorからレスポンス作成
   * @param cb
   * @param {object} error Error
   * @returns {object} エラー内容
   *
   * @example
   * // Example 1: Basic error response
   * this.createErrorResponse(callback, {
   *   status: 400,
   *   message: 'Invalid request'
   * })
   *
   * @example
   * // Example 2: Error with messageKey (Auto localized)
   * this.createErrorResponse(callback, {
   *   status: 400,
   *   messageKey: 'E000027'
   * })
   * // If language is 'ja': { status: 400, messageKey: 'E000027', message: 'トークンが無効です。' }
   * // If language is 'en': { status: 400, messageKey: 'E000027', message: 'Invalid token.' }
   *
   */
  createErrorResponse(cb, error) {
    let response = error
    if (!Object.keys(error).includes('status')) {
      response = {
        status: 500,
        message: this.define.message.E900001,
      }
    }

    // Handle messageKey for localized messages
    if (error.messageKey && this.define.message[error.messageKey]) {
      response = {
        status: error.status || 400,
        name: error.name,
        messageKey: error.messageKey,
        message: this.define.message[error.messageKey],
        ...error,
      }
    } else if (
      Object.keys(error).includes('status') &&
      Object.keys(this.define.message).includes(error.message)
    ) {
      response = {
        status: error.status,
        message: this.define.message[error.message],
      }
    }

    // Handle success status (200) as success response
    if (response.status === 200) {
      const apiResponse = {
        statusCode: 200,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Credentials': true,
          'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
          'Access-Control-Allow-Headers':
            'Content-Type, Authorization, X-Requested-With',
        },
        body: JSON.stringify(response),
        isBase64Encoded: false,
      }
      return cb(null, apiResponse)
    }

    console.log('error', error)
    return Promise.resolve()
      .then(() => {
        if (response.status >= 500) {
          return this.noticeToSlack(error)
        }
        return Promise.resolve()
      })
      .then(() => {
        // Return proper API Gateway error response format
        const apiResponse = {
          statusCode: response.status || 500,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Credentials': true,
            'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
            'Access-Control-Allow-Headers':
              'Content-Type, Authorization, X-Requested-With',
          },
          body: JSON.stringify(response),
          isBase64Encoded: false,
        }
        return cb(null, apiResponse)
      })
      .catch(() => {
        // Fallback error response
        const apiResponse = {
          statusCode: response.status || 500,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Credentials': true,
            'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
            'Access-Control-Allow-Headers':
              'Content-Type, Authorization, X-Requested-With',
          },
          body: JSON.stringify(response),
          isBase64Encoded: false,
        }
        return cb(null, apiResponse)
      })
  }

  /**
   * トークン発行
   * @param {Json} data トークンに含まれるデータ
   * @returns {object} - JWTトークン
   */
  jwtSign(data) {
    return Jwt.sign(data, process.env.JWT_KEY, {
      expiresIn: process.env.TOKEN_EXPIRES,
    })
  }

  checkResponseSize(response) {
    return new Promise((resolve, reject) => {
      console.log(JSON.stringify(response).length)
      if (
        response &&
        JSON.stringify(response).length >
        Number.parseInt(process.env.MAX_RESPONSE_SIZE, 10)
      ) {
        return reject({
          status: 413,
          message: this.define.message.E700001,
        })
      }
      return resolve(response)
    })
  }

  /**
   * InvokeType確認
   * @param {Json} e lambdaエベントオブジェクト
   * @returns {Promise} コールバック関数
   */
  startRequest(e) {
    console.log('🚀startRequest(), log of event : ', e)
    const authHeader = e.headers.Authorization || e.headers.authorization
    return new Promise((resolve) => {
      try {
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
          console.log('🚀startRequest(): User not login yet!:', authHeader)
        }
        const token = authHeader.replace('Bearer ', '')
        // Decode without verification for public endpoint
        const decoded = jwt.decode(token)

        if (decoded && decoded['custom:member_no']) {
          const userInfo = {
            member_no: parseInt(decoded['custom:member_no'], 10),
            user_no: decoded['custom:user_no']
              ? parseInt(decoded['custom:user_no'], 10)
              : null,
            member_name: decoded['custom:member_name'] || null,
            email: decoded.email || null,
          }
          console.log(
            `🚀startRequest(): [${process.env.LAMBDA_FUNCTION_NAME}] , user information:`,
            userInfo
          )
        }
      } catch (error) {
        console.log(
          '🚀startRequest(): Error extracting member info from token:',
          error
        )
      }
      return resolve()
    })
  }

  noticeToSlack(error) {
    return new Promise((resolve, reject) => {
      const region = process.env.AWS_REGION || 'ap-northeast-1'
      const functionName = process.env.AWS_LAMBDA_FUNCTION_NAME || ''
      console.log('🔈 log of functionName : ', functionName)
      const logGroupName = `/aws/lambda/${functionName}`
      console.log('🔒 log of logGroupName : ', logGroupName)
      const encodedLogGroupName = encodeURIComponent(logGroupName)
      console.log('👷‍♂️ log of encodedLogGroupName : ', encodedLogGroupName)
      const cloudwatchLink = `https://${region}.console.aws.amazon.com/cloudwatch/home?region=${region}#logsV2:log-groups/log-group/${encodedLogGroupName}`
      const message = `システムエラーが発生しました。
        - リクエスト名：${functionName}
        - エラー内容：${error ? error.message || JSON.stringify(error) : 'エラーメッセージが取得できません。'}
        - ログ：${cloudwatchLink}`
      return this.invokeLambda(process.env.NOTICE_SLACK_FUNCTION, {
        message,
      })
        .then(resolve)
        .catch((error) => {
          console.log(error)
          return resolve()
        })
    })
  }

  invokeLambda(functionArn, event) {
    const client = new LambdaClient({})
    const command = new InvokeCommand({
      FunctionName: functionArn,
      Payload: JSON.stringify(event),
      InvocationType: 'RequestResponse',
      LogType: LogType.Tail,
    })
    return Promise.resolve()
      .then(() => client.send(command))
      .then(({Payload, LogResult}) => {
        const payload = Buffer.from(Payload).toJSON()
        const logs = Buffer.from(LogResult, 'base64').toString()
        console.log('Payload: ', payload)
        console.log('Logs: ', logs)
        if (payload.errorType || payload.errorMessage) {
          return Promise.reject(payload.errorMessage)
        }
        return Promise.resolve(payload)
      })
  }

  /**
   * Check origin(domain name) and get tenant info
   * params: String, exg. 'https://example.jp',
   * Output: Object ,exg. { tenant_no: 2 }
   * @param origin
   */
  checkOrigin(origin) {
    console.log('log of origin : ', origin)
    return this.pool
      .byPassQuery('CHECK-ORIGIN', 'SELECT * FROM "f_get_tenant_info"($1);', [
        origin,
      ])
      .then((result) => {
        console.log('🏢 log of tenant info : ', result)
        if (result.length === 0) {
          const response = {
            status: 401,
            message: this.define.message.E800003,
          }
          return Promise.reject(response)
        }
        return Promise.resolve(result[0])
      })
  }

  maskPrivateInfo(authorizer, data, masks) {
    if (!authorizer.user_no) {
      for (const key of Object.keys(data)) {
        if (typeof data[key] === 'object') {
          data[key] = this.maskPrivateInfo(authorizer, data[key], masks)
        } else if (Object.keys(masks).includes(key)) {
          data[key] = masks[key]
        }
      }
    }
    return data
  }

  /**
   * Randomの文字列を作成する
   * @param length 文字列の長さ
   */
  randomString(length) {
    let result = ''
    const characters =
      'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
    const charactersLength = characters.length
    for (let i = 0; i < length; i++) {
      result += characters.charAt(Math.floor(Math.random() * charactersLength))
    }
    return result
  }

  /**
   * CSV項目名を変換する
   * @param {object} data CSVコンテンツ（Jsonの形）
   * @param {object} COLUMN_NAME CSV項目名定数マスター
   * @returns {Promise} コールバック関数
   */
  updateColumnName(data, COLUMN_NAME) {
    return new Promise((resolve, reject) => {
      try {
        if (data && data.length > 0) {
          // Create new order following COLUMN_NAME's order
          const newData = []
          for (const json of data) {
            const tmpLine = {}
            for (const key of Object.keys(COLUMN_NAME)) {
              tmpLine[COLUMN_NAME[key]] =
                typeof json[key] === 'undefined' || json[key] === null
                  ? ''
                  : json[key]
            }
            newData.push(tmpLine)
          }
          return resolve(newData)
        }
        return reject({
          status: 400,
          errors: {
            message: this.define.message.E000017,
          },
        })
      } catch (error) {
        console.log(error)
        return reject(error)
      }
    })
  }

  /**
   * Jsonの形からCSVに変換し、S3にアップロードする
   * @param {object} data CSVコンテンツ（Jsonの形）
   * @param {object} CSV_DEFINE CSVの定数
   * @param name
   * @returns {Promise} コールバック関数
   */
  uploadCsvToS3(data, CSV_DEFINE, name) {
    const client = new S3Client({})
    const now = new Date()
    const y = now.getFullYear()
    const m = `00${now.getMonth() + 1}`.slice(-2)
    const d = `00${now.getDate()}`.slice(-2)
    const hh = `00${now.getHours()}`.slice(-2)
    const mi = `00${now.getMinutes()}`.slice(-2)
    const ss = `00${now.getSeconds()}`.slice(-2)
    const timestamp = y + m + d + hh + mi + ss
    const s3Key = `csv-download/${timestamp}/${name}.csv`
    return Promise.resolve(data.map((x) => x.csv_row).join('\r\n'))
      .then(() => {
        return this.updateColumnName(data, CSV_DEFINE.COLUMN_NAME)
      })
      .then((result) => {
        const field = Object.values(result[0])
        const opts = {field}
        return parseAsync(result, opts)
      })
      .then((csv) => {
        csv = `${csv.split('\n').join('\r\n')}\r\n`
        const params = {
          Bucket: process.env.S3_BUCKET,
          Key: s3Key,
          Body: iconv.encode(csv, 'Shift_JIS'),
          ContentType: 'text/csv charset=Shift_JIS',
          ContentDisposition: 'attachment',
        }
        const command = new PutObjectCommand(params)
        return client.send(command)
      })
      .then(() => {
        // get pre-signed url
        const expiresIn = Number.parseInt(process.env.CSV_EXPIRES, 10)
        const getCommand = new GetObjectCommand({
          Bucket: process.env.S3_BUCKET,
          Key: s3Key,
        })
        return getSignedUrl(client, getCommand, {expiresIn})
      })
  }
  // 値が「-」(数値以外)や空文字の場合は単位を表示しないようにする
  number2string(val, nullText = '', unit = '', NaNVal) {
    if (
      typeof val === 'undefined' ||
      val === null ||
      String(val).length === 0
    ) {
      return nullText
    }
    if (isNaN(val)) {
      return typeof NaNVal === 'undefined' ? String(val) : NaNVal
    }
    return Common.numberStringWithComma(val) + unit
  }

  // Get payment gateway info
  getPaymentGatewayInfo(tenantNo) {
    if (!tenantNo) {
      const error = {
        status: 400,
        errors: {card_error: this.define.message.E300005},
      }
      return Promise.reject(error)
    }
    console.log('log of tenantNo : ', tenantNo)
    // Get payment gateway info
    return this.pool
      .rlsQuery(tenantNo, 'SELECT * FROM "f_get_payment_gateway_info"($1);', [
        tenantNo,
      ])
      .then((result) => {
        const data = result.length > 0 ? result[0] : null
        if (!data) {
          console.log('No payment gateway info found')
          const error = {
            status: 400,
            errors: {pg_error: this.define.message.E300005},
          }
          return Promise.reject(error)
        }
        const tmp = {
          payment_url: data.ext_link_options?.payment_url || '',
          site_id: data.ext_link_options?.site_id || '',
          site_pass: data.ext_link_options?.site_password || '',
          shop_id: data.ext_link_options?.shop_id || '',
          shop_pass: data.ext_link_options?.shop_password || '',
        }
        console.log('log of payment gateway info : ', tmp)
        // Check if payment gateway info is complete
        if (
          !tmp.payment_url ||
          !tmp.site_id ||
          !tmp.site_pass ||
          !tmp.shop_id ||
          !tmp.shop_pass
        ) {
          console.log('Incomplete payment gateway info')
          const error = {
            status: 400,
            errors: {pg_error: this.define.message.E300005},
          }
          return Promise.reject(error)
        }
        return Promise.resolve(tmp)
      })
  }
}

module.exports = Base
