const Base = require(`${process.env.COMMON_LAYER_PATH}base.js`)
const Common = require(`${process.env.COMMON_LAYER_PATH}common.js`)
const PgPool = require(`${process.env.COMMON_LAYER_PATH}pg-pool.js`)
const pool = new PgPool(process.env.PGHOST)

exports.handle = (e, ctx, cb) => {
  const params = Common.parseRequestBody(e.body)
  console.log('👷‍♂️ log of params : ', params)
  const authorizer = {
    user_no: Common.extractUserNo(e),
    member_name: Common.extractMemberName(e),
    member_no: Common.extractMemberNo(e),
    tenant_no: Common.extractTenantId(e),
  }
  console.log('🔒 log of authorizer : ', authorizer)

  const base = new Base(pool, params.languageCode)

  Promise.resolve()
    .then(() => base.startRequest(e))
    .then(() => {
      const sqlParams = [
        authorizer.member_no,
        authorizer.tenant_no,
      ]
      const sql = `SELECT * FROM "f_get_member_status"(${Common.sqlParamNumber(sqlParams.length)});`
      return pool.rlsQuery(authorizer.tenant_no, sql, sqlParams)
    })
    .then(result => {
      const responseParams = result.length > 0 ? {status: result[0].status} : {status: null}
      return base.createSuccessResponse(cb, responseParams)
    })
    .catch(error => base.createErrorResponse(cb, error))
}
